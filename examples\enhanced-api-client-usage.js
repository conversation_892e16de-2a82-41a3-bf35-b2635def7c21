/**
 * @title Enhanced API Client Usage Examples - أمثلة استخدام عميل API المحسن
 * @description أمثلة شاملة لاستخدام عميل API المحسن مع أفضل الممارسات
 * <AUTHOR> Team
 * @version 2.0.0
 */

const EnhancedAPIClient = require('../utils/EnhancedAPIClient');
const { printConfigSummary } = require('../config/api-client-config');

/**
 * مثال أساسي لاستخدام عميل API المحسن
 */
async function basicUsageExample() {
    console.log('🚀 مثال الاستخدام الأساسي لعميل API المحسن\n');
    
    try {
        // إنشاء عميل API محسن
        const apiClient = new EnhancedAPIClient();
        
        // طباعة ملخص التكوين
        printConfigSummary();
        console.log('');
        
        // مثال على طلب GET بسيط
        console.log('📤 إرسال طلب GET...');
        const response = await apiClient.get('/system/status');
        console.log('📥 الاستجابة:', response.data);
        console.log('');
        
        // مثال على طلب POST
        console.log('📤 إرسال طلب POST...');
        const postResponse = await apiClient.post('/strategies/arbitrage/optimize', {
            parameters: {
                minProfitBps: 50,
                maxSlippageBps: 100,
                riskLevel: 5
            }
        });
        console.log('📥 نتيجة التحسين:', postResponse.data);
        
    } catch (error) {
        console.error('❌ خطأ في المثال الأساسي:', error.message);
    }
}

/**
 * مثال متقدم مع معالجة الأخطاء والمراقبة
 */
async function advancedUsageExample() {
    console.log('🔧 مثال الاستخدام المتقدم مع المراقبة\n');
    
    try {
        // إنشاء عميل مع تكوين مخصص
        const apiClient = new EnhancedAPIClient({
            connection: {
                timeout: 15000, // 15 ثانية
                maxRetries: 5
            },
            security: {
                rateLimiting: {
                    maxRequestsPerSecond: 5,
                    maxRequestsPerMinute: 50
                }
            },
            caching: {
                enabled: true,
                ttl: {
                    default: 60000 // دقيقة واحدة
                }
            }
        });
        
        // إعداد مستمعي الأحداث للمراقبة
        setupEventListeners(apiClient);
        
        // مثال على طلبات متعددة مع التخزين المؤقت
        console.log('📊 اختبار التخزين المؤقت...');
        
        // الطلب الأول - سيتم تنفيذه
        const start1 = Date.now();
        const response1 = await apiClient.get('/system/stats');
        console.log(`⏱️ الطلب الأول: ${Date.now() - start1}ms`);
        
        // الطلب الثاني - سيأتي من التخزين المؤقت
        const start2 = Date.now();
        const response2 = await apiClient.get('/system/stats');
        console.log(`⏱️ الطلب الثاني: ${Date.now() - start2}ms ${response2.fromCache ? '(من التخزين المؤقت)' : ''}`);
        
        // مثال على طلبات متوازية
        console.log('\n🔄 اختبار الطلبات المتوازية...');
        const parallelRequests = [
            apiClient.get('/strategies'),
            apiClient.get('/networks'),
            apiClient.get('/monitoring/metrics')
        ];
        
        const parallelResults = await Promise.allSettled(parallelRequests);
        parallelResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                console.log(`✅ الطلب ${index + 1}: نجح`);
            } else {
                console.log(`❌ الطلب ${index + 1}: فشل - ${result.reason.message}`);
            }
        });
        
        // عرض مقاييس الأداء
        console.log('\n📊 مقاييس الأداء:');
        const metrics = apiClient.getMetrics();
        console.log(`   📈 إجمالي الطلبات: ${metrics.totalRequests}`);
        console.log(`   ✅ الطلبات الناجحة: ${metrics.successfulRequests}`);
        console.log(`   ❌ الطلبات الفاشلة: ${metrics.failedRequests}`);
        console.log(`   ⏱️ متوسط وقت الاستجابة: ${metrics.averageResponseTime.toFixed(2)}ms`);
        console.log(`   💾 حجم التخزين المؤقت: ${metrics.cache.size}`);
        console.log(`   🔄 حالة Circuit Breaker: ${metrics.circuitBreakerState}`);
        
    } catch (error) {
        console.error('❌ خطأ في المثال المتقدم:', error.message);
    }
}

/**
 * مثال على معالجة الأخطاء المتقدمة
 */
async function errorHandlingExample() {
    console.log('🛡️ مثال معالجة الأخطاء المتقدمة\n');
    
    try {
        const apiClient = new EnhancedAPIClient({
            requestManagement: {
                retry: {
                    enabled: true,
                    maxAttempts: 3,
                    baseDelay: 1000,
                    exponentialBackoff: true
                },
                circuitBreaker: {
                    enabled: true,
                    failureThreshold: 3,
                    resetTimeout: 10000
                }
            },
            errorHandling: {
                fallback: {
                    enabled: true,
                    endpoints: [
                        'http://backup-api.flashbot.com',
                        'http://fallback-api.flashbot.com'
                    ]
                }
            }
        });
        
        // محاولة طلب لنقطة نهاية غير موجودة
        console.log('🔍 اختبار طلب لنقطة نهاية غير موجودة...');
        try {
            await apiClient.get('/non-existent-endpoint');
        } catch (error) {
            console.log(`❌ خطأ متوقع: ${error.message}`);
            console.log(`   📊 رمز الحالة: ${error.status}`);
            console.log(`   🆔 معرف الطلب: ${error.requestId}`);
        }
        
        // محاولة طلب بمفتاح API خاطئ
        console.log('\n🔑 اختبار مفتاح API خاطئ...');
        const unauthorizedClient = new EnhancedAPIClient({
            security: {
                apiKey: 'invalid-api-key'
            }
        });
        
        try {
            await unauthorizedClient.get('/system/config');
        } catch (error) {
            console.log(`❌ خطأ مصادقة متوقع: ${error.message}`);
        }
        
        // اختبار Rate Limiting
        console.log('\n⏱️ اختبار Rate Limiting...');
        const rateLimitedClient = new EnhancedAPIClient({
            security: {
                rateLimiting: {
                    enabled: true,
                    maxRequestsPerSecond: 2,
                    maxRequestsPerMinute: 5
                }
            }
        });
        
        // إرسال طلبات سريعة لتجاوز الحد
        const rapidRequests = [];
        for (let i = 0; i < 6; i++) {
            rapidRequests.push(
                rateLimitedClient.get('/system/status').catch(error => ({
                    error: error.message,
                    index: i
                }))
            );
        }
        
        const rapidResults = await Promise.allSettled(rapidRequests);
        rapidResults.forEach((result, index) => {
            if (result.status === 'fulfilled' && !result.value.error) {
                console.log(`✅ طلب ${index + 1}: نجح`);
            } else {
                const errorMsg = result.value?.error || result.reason?.message;
                console.log(`❌ طلب ${index + 1}: ${errorMsg}`);
            }
        });
        
    } catch (error) {
        console.error('❌ خطأ في مثال معالجة الأخطاء:', error.message);
    }
}

/**
 * مثال على الاستخدام في بيئة الإنتاج
 */
async function productionUsageExample() {
    console.log('🏭 مثال الاستخدام في بيئة الإنتاج\n');
    
    try {
        // تكوين محسن لبيئة الإنتاج
        const productionClient = new EnhancedAPIClient({
            connection: {
                timeout: 30000,
                maxRetries: 5,
                keepAlive: true
            },
            security: {
                rateLimiting: {
                    enabled: true,
                    maxRequestsPerSecond: 10,
                    maxRequestsPerMinute: 100
                },
                encryption: {
                    enabled: false // تفعيل في الإنتاج الحقيقي
                }
            },
            requestManagement: {
                retry: {
                    enabled: true,
                    maxAttempts: 5,
                    exponentialBackoff: true
                },
                circuitBreaker: {
                    enabled: true,
                    failureThreshold: 5,
                    resetTimeout: 60000
                },
                requestQueue: {
                    enabled: true,
                    maxSize: 1000,
                    concurrency: 10,
                    priority: true
                }
            },
            caching: {
                enabled: true,
                type: 'memory',
                ttl: {
                    default: 300000, // 5 دقائق
                    short: 60000,    // دقيقة
                    long: 3600000    // ساعة
                }
            },
            monitoring: {
                metrics: {
                    enabled: true,
                    collectInterval: 60000
                },
                requestLogging: {
                    enabled: true,
                    level: 'info',
                    includeHeaders: false,
                    includeBody: false
                }
            }
        });
        
        // إعداد مراقبة متقدمة
        setupProductionMonitoring(productionClient);
        
        // مثال على عمليات تداول حقيقية
        console.log('💰 تنفيذ عمليات تداول...');
        
        // الحصول على الفرص المتاحة
        const opportunities = await productionClient.get('/analytics/opportunities', {
            params: {
                network: 'arbitrum',
                strategy: 'arbitrage',
                minProfit: 100
            },
            cacheTTL: 30000 // 30 ثانية
        });
        
        console.log(`🎯 تم العثور على ${opportunities.data.length} فرصة`);
        
        // تنفيذ أفضل فرصة (محاكاة)
        if (opportunities.data.length > 0) {
            const bestOpportunity = opportunities.data[0];
            console.log(`🚀 تنفيذ أفضل فرصة: ${bestOpportunity.id}`);
            
            const execution = await productionClient.post(
                `/opportunities/${bestOpportunity.id}/execute`,
                {
                    amount: bestOpportunity.optimalAmount,
                    slippage: 0.5,
                    gasPrice: 'fast'
                },
                {
                    priority: 10, // أولوية عالية
                    timeout: 45000 // 45 ثانية
                }
            );
            
            console.log(`✅ تم التنفيذ بنجاح: ${execution.data.transactionHash}`);
        }
        
        // مراقبة الأداء
        setTimeout(() => {
            const metrics = productionClient.getMetrics();
            console.log('\n📊 تقرير الأداء النهائي:');
            console.log(`   🎯 معدل النجاح: ${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(2)}%`);
            console.log(`   ⚡ متوسط الاستجابة: ${metrics.averageResponseTime.toFixed(2)}ms`);
            console.log(`   💾 كفاءة التخزين المؤقت: ${(metrics.cache.hitRate * 100).toFixed(2)}%`);
        }, 5000);
        
    } catch (error) {
        console.error('❌ خطأ في مثال الإنتاج:', error.message);
    }
}

/**
 * إعداد مستمعي الأحداث للمراقبة
 */
function setupEventListeners(apiClient) {
    // مراقبة نجاح الطلبات
    apiClient.on('requestSuccess', (data) => {
        console.log(`✅ [${data.requestId}] ${data.method} ${data.url} - ${data.duration}ms`);
    });
    
    // مراقبة أخطاء الطلبات
    apiClient.on('requestError', (data) => {
        console.log(`❌ [${data.requestId}] خطأ ${data.type}: ${data.error.message}`);
    });
    
    // مراقبة نجاح التخزين المؤقت
    apiClient.on('cacheHit', (data) => {
        console.log(`💾 [Cache Hit] ${data.url}`);
    });
    
    // مراقبة مقاييس الأداء
    apiClient.on('metrics', (metrics) => {
        if (metrics.totalRequests % 10 === 0) { // كل 10 طلبات
            console.log(`📊 [Metrics] الطلبات: ${metrics.totalRequests}, النجاح: ${metrics.successfulRequests}, المتوسط: ${metrics.averageResponseTime.toFixed(2)}ms`);
        }
    });
}

/**
 * إعداد مراقبة الإنتاج المتقدمة
 */
function setupProductionMonitoring(apiClient) {
    // تسجيل مفصل للأخطاء الحرجة
    apiClient.on('requestError', (data) => {
        if (data.status >= 500) {
            console.error(`🚨 [CRITICAL] خطأ خادم: ${data.error.message}`);
            // في الإنتاج: إرسال تنبيه للفريق
        }
    });
    
    // مراقبة Circuit Breaker
    apiClient.on('metrics', (metrics) => {
        if (metrics.circuitBreakerState === 'OPEN') {
            console.warn('⚠️ [WARNING] Circuit Breaker مفتوح - الخدمة متدهورة');
            // في الإنتاج: إرسال تنبيه فوري
        }
    });
    
    // مراقبة الأداء
    apiClient.on('metrics', (metrics) => {
        if (metrics.averageResponseTime > 5000) { // أكثر من 5 ثوان
            console.warn(`⚠️ [PERFORMANCE] استجابة بطيئة: ${metrics.averageResponseTime.toFixed(2)}ms`);
        }
        
        const errorRate = (metrics.failedRequests / metrics.totalRequests) * 100;
        if (errorRate > 10) { // أكثر من 10% أخطاء
            console.warn(`⚠️ [ERROR_RATE] معدل أخطاء مرتفع: ${errorRate.toFixed(2)}%`);
        }
    });
}

/**
 * مثال شامل يجمع كل الميزات
 */
async function comprehensiveExample() {
    console.log('🌟 المثال الشامل - جميع الميزات\n');
    
    try {
        // إنشاء عميل بتكوين شامل
        const comprehensiveClient = new EnhancedAPIClient();
        
        // إعداد المراقبة
        setupEventListeners(comprehensiveClient);
        
        console.log('🔄 تنفيذ سيناريو تداول شامل...\n');
        
        // 1. فحص حالة النظام
        console.log('1️⃣ فحص حالة النظام...');
        const systemStatus = await comprehensiveClient.get('/system/status');
        console.log(`   ✅ حالة النظام: ${systemStatus.data.status}\n`);
        
        // 2. الحصول على قائمة الاستراتيجيات
        console.log('2️⃣ جلب قائمة الاستراتيجيات...');
        const strategies = await comprehensiveClient.get('/strategies');
        console.log(`   📊 عدد الاستراتيجيات: ${strategies.data.length}\n`);
        
        // 3. تحليل أداء استراتيجية محددة
        console.log('3️⃣ تحليل أداء الاستراتيجية...');
        const performance = await comprehensiveClient.get('/strategies/arbitrage/performance');
        console.log(`   📈 معدل النجاح: ${(performance.data.successRate * 100).toFixed(2)}%\n`);
        
        // 4. البحث عن فرص التداول
        console.log('4️⃣ البحث عن فرص التداول...');
        const opportunities = await comprehensiveClient.get('/analytics/opportunities', {
            params: {
                network: 'arbitrum',
                minProfit: 50
            }
        });
        console.log(`   🎯 الفرص المتاحة: ${opportunities.data.length}\n`);
        
        // 5. الحصول على تنبؤات الأسعار
        console.log('5️⃣ جلب تنبؤات الأسعار...');
        const predictions = await comprehensiveClient.get('/predictions/ETH', {
            params: { timeframe: '1hour' }
        });
        console.log(`   🔮 ثقة التنبؤ: ${(predictions.data.confidence * 100).toFixed(2)}%\n`);
        
        // 6. مراقبة مقاييس النظام
        console.log('6️⃣ مراقبة مقاييس النظام...');
        const metrics = await comprehensiveClient.get('/monitoring/metrics');
        console.log(`   💻 استخدام المعالج: ${metrics.data.cpu.toFixed(2)}%`);
        console.log(`   🧠 استخدام الذاكرة: ${metrics.data.memory.toFixed(2)}%\n`);
        
        // 7. عرض التقرير النهائي
        console.log('📊 التقرير النهائي:');
        const finalMetrics = comprehensiveClient.getMetrics();
        console.log(`   📈 إجمالي الطلبات: ${finalMetrics.totalRequests}`);
        console.log(`   ✅ معدل النجاح: ${((finalMetrics.successfulRequests / finalMetrics.totalRequests) * 100).toFixed(2)}%`);
        console.log(`   ⏱️ متوسط الاستجابة: ${finalMetrics.averageResponseTime.toFixed(2)}ms`);
        console.log(`   💾 حجم التخزين المؤقت: ${finalMetrics.cache.size}`);
        
        // تنظيف الموارد
        await comprehensiveClient.shutdown();
        console.log('\n✅ تم إكمال المثال الشامل بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في المثال الشامل:', error.message);
    }
}

/**
 * تشغيل جميع الأمثلة
 */
async function runAllExamples() {
    console.log('🎯 تشغيل جميع أمثلة عميل API المحسن\n');
    console.log('='.repeat(60));
    
    try {
        await basicUsageExample();
        console.log('\n' + '='.repeat(60));
        
        await advancedUsageExample();
        console.log('\n' + '='.repeat(60));
        
        await errorHandlingExample();
        console.log('\n' + '='.repeat(60));
        
        await productionUsageExample();
        console.log('\n' + '='.repeat(60));
        
        await comprehensiveExample();
        
    } catch (error) {
        console.error('❌ خطأ في تشغيل الأمثلة:', error.message);
    }
    
    console.log('\n🎉 تم الانتهاء من جميع الأمثلة');
}

// تشغيل الأمثلة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    runAllExamples().catch(console.error);
}

module.exports = {
    basicUsageExample,
    advancedUsageExample,
    errorHandlingExample,
    productionUsageExample,
    comprehensiveExample,
    runAllExamples
};
