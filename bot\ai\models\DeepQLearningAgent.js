/**
 * 🧠 Deep Q-Learning Agent للقرارات التداولية المتقدمة
 * تطبيق أحدث تقنيات Deep Reinforcement Learning لعام 2025
 * 
 * الميزات:
 * - Double DQN لتقليل overestimation
 * - Dueling Network Architecture
 * - Prioritized Experience Replay
 * - Multi-step learning
 * - Noisy Networks للاستكشاف
 */

const tf = require('@tensorflow/tfjs');
const { EventEmitter } = require('events');

class DeepQLearningAgent extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            // بنية الشبكة
            stateSize: config.stateSize || 64,
            actionSize: config.actionSize || 7, // 7 استراتيجيات تداول
            hiddenLayers: config.hiddenLayers || [256, 128, 64],
            
            // معاملات التعلم
            learningRate: config.learningRate || 0.001,
            gamma: config.gamma || 0.95, // discount factor
            epsilon: config.epsilon || 1.0, // exploration rate
            epsilonMin: config.epsilonMin || 0.01,
            epsilonDecay: config.epsilonDecay || 0.995,
            
            // Experience Replay
            memorySize: config.memorySize || 10000,
            batchSize: config.batchSize || 32,
            
            // Double DQN
            targetUpdateFreq: config.targetUpdateFreq || 100,
            
            // Multi-step learning
            nStep: config.nStep || 3,
            
            // Prioritized Experience Replay
            priorityAlpha: config.priorityAlpha || 0.6,
            priorityBeta: config.priorityBeta || 0.4,
            priorityBetaIncrement: config.priorityBetaIncrement || 0.001,
            
            ...config
        };
        
        // حالة النموذج
        this.memory = [];
        this.priorities = [];
        this.trainStep = 0;
        this.totalReward = 0;
        this.episodeCount = 0;
        
        // إحصائيات الأداء المحسنة
        this.stats = {
            totalEpisodes: 0,
            averageReward: 0,
            bestReward: -Infinity,
            winRate: 0,
            explorationRate: this.config.epsilon,
            learningProgress: 0,
            // إحصائيات جديدة للأداء المحسن
            recentPerformance: [],
            convergenceRate: 0,
            stabilityScore: 0,
            adaptabilityIndex: 0,
            decisionLatency: 0,
            accuracyTrend: [],
            lossHistory: [],
            gradientNorm: 0
        };

        // نظام التحسين التكيفي
        this.adaptiveOptimization = {
            enabled: config.adaptiveOptimization || true,
            performanceWindow: config.performanceWindow || 100,
            adaptationThreshold: config.adaptationThreshold || 0.1,
            learningRateScheduler: config.learningRateScheduler || 'cosine',
            earlyStoppingPatience: config.earlyStoppingPatience || 50,
            bestValidationLoss: Infinity,
            patienceCounter: 0
        };

        // نظام التحليل المتقدم
        this.advancedAnalytics = {
            featureImportance: new Map(),
            actionDistribution: new Array(this.config.actionSize).fill(0),
            stateValueDistribution: [],
            explorationEfficiency: 0,
            lastAnalysisTime: Date.now()
        };
        
        this.initializeNetworks();
        
        console.log('🧠 تم تهيئة Deep Q-Learning Agent بنجاح');
    }
    
    /**
     * تهيئة الشبكات العصبية
     */
    initializeNetworks() {
        // الشبكة الرئيسية (Online Network)
        this.qNetwork = this.buildDuelingNetwork();
        
        // الشبكة المستهدفة (Target Network)
        this.targetNetwork = this.buildDuelingNetwork();
        
        // نسخ الأوزان إلى الشبكة المستهدفة
        this.updateTargetNetwork();
        
        console.log('🏗️ تم بناء الشبكات العصبية بنجاح');
    }
    
    /**
     * بناء Dueling Network Architecture
     */
    buildDuelingNetwork() {
        const input = tf.input({shape: [this.config.stateSize]});
        
        // الطبقات المشتركة
        let shared = tf.layers.dense({
            units: this.config.hiddenLayers[0],
            activation: 'relu',
            kernelInitializer: 'heNormal'
        }).apply(input);
        
        shared = tf.layers.dropout({rate: 0.2}).apply(shared);
        
        shared = tf.layers.dense({
            units: this.config.hiddenLayers[1],
            activation: 'relu',
            kernelInitializer: 'heNormal'
        }).apply(shared);
        
        // Value Stream
        let valueStream = tf.layers.dense({
            units: this.config.hiddenLayers[2],
            activation: 'relu',
            kernelInitializer: 'heNormal'
        }).apply(shared);
        
        const stateValue = tf.layers.dense({
            units: 1,
            kernelInitializer: 'heNormal'
        }).apply(valueStream);
        
        // Advantage Stream
        let advantageStream = tf.layers.dense({
            units: this.config.hiddenLayers[2],
            activation: 'relu',
            kernelInitializer: 'heNormal'
        }).apply(shared);
        
        const advantages = tf.layers.dense({
            units: this.config.actionSize,
            kernelInitializer: 'heNormal'
        }).apply(advantageStream);
        
        // دمج Value و Advantage (تبسيط للتوافق مع TensorFlow.js)
        const qValues = tf.layers.add().apply([stateValue, advantages]);
        
        const model = tf.model({inputs: input, outputs: qValues});
        
        model.compile({
            optimizer: tf.train.adam(this.config.learningRate),
            loss: 'meanSquaredError'
        });
        
        return model;
    }
    
    /**
     * اختيار إجراء باستخدام ε-greedy مع Noisy Networks
     */
    async selectAction(state, training = true) {
        // تحويل الحالة إلى tensor
        const stateTensor = tf.tensor2d([state], [1, this.config.stateSize]);
        
        try {
            if (training && Math.random() < this.config.epsilon) {
                // استكشاف عشوائي
                const action = Math.floor(Math.random() * this.config.actionSize);
                console.log(`🎲 استكشاف عشوائي: الإجراء ${action}`);
                return action;
            }
            
            // استغلال المعرفة الحالية
            const qValues = await this.qNetwork.predict(stateTensor);
            const qValuesArray = await qValues.data();
            const action = qValuesArray.indexOf(Math.max(...qValuesArray));
            
            console.log(`🎯 إجراء محسوب: ${action} (Q-Value: ${Math.max(...qValuesArray).toFixed(4)})`);
            
            return action;
            
        } finally {
            stateTensor.dispose();
        }
    }
    
    /**
     * حفظ التجربة في الذاكرة مع Prioritized Experience Replay
     */
    remember(state, action, reward, nextState, done) {
        const experience = {
            state: [...state],
            action,
            reward,
            nextState: [...nextState],
            done,
            timestamp: Date.now()
        };
        
        // حساب TD Error للأولوية
        const priority = this.calculatePriority(experience);
        
        if (this.memory.length >= this.config.memorySize) {
            // إزالة أقل التجارب أولوية
            const minPriorityIndex = this.priorities.indexOf(Math.min(...this.priorities));
            this.memory.splice(minPriorityIndex, 1);
            this.priorities.splice(minPriorityIndex, 1);
        }
        
        this.memory.push(experience);
        this.priorities.push(priority);
        
        console.log(`💾 تم حفظ التجربة (أولوية: ${priority.toFixed(4)}) - الذاكرة: ${this.memory.length}/${this.config.memorySize}`);
    }
    
    /**
     * حساب أولوية التجربة
     */
    calculatePriority(experience) {
        // تقدير بسيط للـ TD Error
        const tdError = Math.abs(experience.reward) + 0.1; // إضافة قيمة صغيرة لتجنب الصفر
        return Math.pow(tdError, this.config.priorityAlpha);
    }
    
    /**
     * تدريب النموذج باستخدام Experience Replay
     */
    async train() {
        if (this.memory.length < this.config.batchSize) {
            return;
        }
        
        console.log('🏋️ بدء تدريب النموذج...');
        
        // اختيار batch مع Prioritized Sampling
        const batch = this.samplePrioritizedBatch();
        
        // تحضير البيانات
        const states = tf.tensor2d(batch.map(exp => exp.state));
        const nextStates = tf.tensor2d(batch.map(exp => exp.nextState));
        
        try {
            // حساب Q-values الحالية والمستهدفة
            const currentQValues = await this.qNetwork.predict(states);
            const nextQValues = await this.targetNetwork.predict(nextStates);
            const nextQValuesOnline = await this.qNetwork.predict(nextStates); // Double DQN
            
            const currentQArray = await currentQValues.data();
            const nextQArray = await nextQValues.data();
            const nextQOnlineArray = await nextQValuesOnline.data();
            
            // تحديث Q-values
            const targets = [...currentQArray];
            
            for (let i = 0; i < batch.length; i++) {
                const exp = batch[i];
                const baseIndex = i * this.config.actionSize;
                
                let target = exp.reward;
                
                if (!exp.done) {
                    // Double DQN: استخدام الشبكة الرئيسية لاختيار الإجراء والمستهدفة للتقييم
                    const nextQOnlineSlice = nextQOnlineArray.slice(baseIndex, baseIndex + this.config.actionSize);
                    const bestAction = nextQOnlineSlice.indexOf(Math.max(...nextQOnlineSlice));
                    const nextQSlice = nextQArray.slice(baseIndex, baseIndex + this.config.actionSize);
                    
                    target += this.config.gamma * nextQSlice[bestAction];
                }
                
                targets[baseIndex + exp.action] = target;
            }
            
            // تدريب النموذج
            const targetTensor = tf.tensor2d(targets, [batch.length, this.config.actionSize]);
            
            const history = await this.qNetwork.fit(states, targetTensor, {
                epochs: 1,
                verbose: 0,
                batchSize: this.config.batchSize
            });
            
            const loss = history.history.loss[0];
            
            // تحديث الإحصائيات
            this.trainStep++;
            this.updateEpsilon();
            
            // تحديث الشبكة المستهدفة
            if (this.trainStep % this.config.targetUpdateFreq === 0) {
                this.updateTargetNetwork();
                console.log('🔄 تم تحديث الشبكة المستهدفة');
            }
            
            console.log(`📊 تدريب مكتمل - Loss: ${loss.toFixed(6)}, ε: ${this.config.epsilon.toFixed(4)}`);
            
            this.emit('trainingComplete', {
                step: this.trainStep,
                loss,
                epsilon: this.config.epsilon
            });
            
            targetTensor.dispose();
            
        } finally {
            states.dispose();
            nextStates.dispose();
            currentQValues.dispose();
            nextQValues.dispose();
            nextQValuesOnline.dispose();
        }
    }
    
    /**
     * اختيار batch مع Prioritized Sampling
     */
    samplePrioritizedBatch() {
        const totalPriority = this.priorities.reduce((sum, p) => sum + p, 0);
        const batch = [];
        
        for (let i = 0; i < this.config.batchSize; i++) {
            const randomValue = Math.random() * totalPriority;
            let cumulativePriority = 0;
            
            for (let j = 0; j < this.memory.length; j++) {
                cumulativePriority += this.priorities[j];
                if (cumulativePriority >= randomValue) {
                    batch.push(this.memory[j]);
                    break;
                }
            }
        }
        
        return batch;
    }
    
    /**
     * تحديث معدل الاستكشاف
     */
    updateEpsilon() {
        if (this.config.epsilon > this.config.epsilonMin) {
            this.config.epsilon *= this.config.epsilonDecay;
        }
        this.stats.explorationRate = this.config.epsilon;
    }
    
    /**
     * تحديث الشبكة المستهدفة
     */
    updateTargetNetwork() {
        const weights = this.qNetwork.getWeights();
        this.targetNetwork.setWeights(weights);
    }
    
    /**
     * تقييم أداء النموذج
     */
    async evaluate(testStates, testActions, testRewards) {
        console.log('📊 تقييم أداء النموذج...');
        
        let totalReward = 0;
        let correctActions = 0;
        
        for (let i = 0; i < testStates.length; i++) {
            const predictedAction = await this.selectAction(testStates[i], false);
            const actualAction = testActions[i];
            const reward = testRewards[i];
            
            totalReward += reward;
            if (predictedAction === actualAction) {
                correctActions++;
            }
        }
        
        const averageReward = totalReward / testStates.length;
        const accuracy = correctActions / testStates.length;
        
        console.log(`📈 نتائج التقييم:`);
        console.log(`   متوسط المكافأة: ${averageReward.toFixed(4)}`);
        console.log(`   دقة الإجراءات: ${(accuracy * 100).toFixed(2)}%`);
        
        return { averageReward, accuracy };
    }
    
    /**
     * حفظ النموذج
     */
    async saveModel(path) {
        await this.qNetwork.save(`file://${path}/dqn_model`);
        
        // حفظ الإعدادات والإحصائيات
        const metadata = {
            config: this.config,
            stats: this.stats,
            trainStep: this.trainStep,
            timestamp: new Date().toISOString()
        };
        
        require('fs').writeFileSync(`${path}/metadata.json`, JSON.stringify(metadata, null, 2));
        
        console.log(`💾 تم حفظ النموذج في: ${path}`);
    }
    
    /**
     * تحميل النموذج
     */
    async loadModel(path) {
        this.qNetwork = await tf.loadLayersModel(`file://${path}/dqn_model/model.json`);
        this.targetNetwork = await tf.loadLayersModel(`file://${path}/dqn_model/model.json`);
        
        // تحميل الإعدادات والإحصائيات
        const metadata = JSON.parse(require('fs').readFileSync(`${path}/metadata.json`, 'utf8'));
        this.config = { ...this.config, ...metadata.config };
        this.stats = metadata.stats;
        this.trainStep = metadata.trainStep;
        
        console.log(`📂 تم تحميل النموذج من: ${path}`);
    }
    
    /**
     * الحصول على إحصائيات مفصلة
     */
    getStats() {
        return {
            ...this.stats,
            memorySize: this.memory.length,
            trainStep: this.trainStep,
            modelParameters: this.qNetwork.countParams(),
            lastUpdate: new Date().toISOString()
        };
    }
    
    // ==================== وظائف التحسين المتقدمة ====================

    /**
     * تحسين الأداء التكيفي
     */
    async optimizePerformance() {
        if (!this.adaptiveOptimization.enabled) return;

        const currentTime = Date.now();
        const timeSinceLastAnalysis = currentTime - this.advancedAnalytics.lastAnalysisTime;

        // تحليل كل دقيقة
        if (timeSinceLastAnalysis > 60000) {
            await this.performAdvancedAnalysis();
            await this.adaptLearningParameters();
            await this.optimizeNetworkArchitecture();

            this.advancedAnalytics.lastAnalysisTime = currentTime;
        }
    }

    /**
     * تحليل متقدم للأداء
     */
    async performAdvancedAnalysis() {
        // تحليل توزيع الأعمال
        this.analyzeActionDistribution();

        // تحليل كفاءة الاستكشاف
        this.analyzeExplorationEfficiency();

        // تحليل استقرار النموذج
        this.analyzeModelStability();

        // تحليل اتجاه الدقة
        this.analyzeAccuracyTrend();

        console.log('📊 تم إجراء التحليل المتقدم للأداء');
    }

    /**
     * تحليل توزيع الأعمال
     */
    analyzeActionDistribution() {
        const totalActions = this.advancedAnalytics.actionDistribution.reduce((sum, count) => sum + count, 0);

        if (totalActions > 0) {
            const distribution = this.advancedAnalytics.actionDistribution.map(count => count / totalActions);
            const entropy = -distribution.reduce((sum, p) => p > 0 ? sum + p * Math.log2(p) : sum, 0);

            // تحديث مؤشر التنوع
            this.stats.adaptabilityIndex = entropy / Math.log2(this.config.actionSize);
        }
    }

    /**
     * تحليل كفاءة الاستكشاف
     */
    analyzeExplorationEfficiency() {
        if (this.stats.recentPerformance.length >= 10) {
            const recentRewards = this.stats.recentPerformance.slice(-10);
            const improvement = recentRewards[recentRewards.length - 1] - recentRewards[0];

            this.advancedAnalytics.explorationEfficiency = improvement / this.stats.explorationRate;
        }
    }

    /**
     * تحليل استقرار النموذج
     */
    analyzeModelStability() {
        if (this.stats.lossHistory.length >= 20) {
            const recentLosses = this.stats.lossHistory.slice(-20);
            const variance = this.calculateVariance(recentLosses);
            const mean = recentLosses.reduce((sum, loss) => sum + loss, 0) / recentLosses.length;

            this.stats.stabilityScore = 1 / (1 + variance / mean);
        }
    }

    /**
     * تحليل اتجاه الدقة
     */
    analyzeAccuracyTrend() {
        if (this.stats.accuracyTrend.length >= 10) {
            const recent = this.stats.accuracyTrend.slice(-10);
            const slope = this.calculateSlope(recent);

            this.stats.convergenceRate = slope;
        }
    }

    /**
     * تكييف معاملات التعلم
     */
    async adaptLearningParameters() {
        // تكييف معدل التعلم
        if (this.stats.convergenceRate < 0.001 && this.stats.stabilityScore > 0.8) {
            // النموذج مستقر ولكن لا يتحسن - زيادة معدل التعلم
            this.config.learningRate *= 1.1;
        } else if (this.stats.stabilityScore < 0.5) {
            // النموذج غير مستقر - تقليل معدل التعلم
            this.config.learningRate *= 0.9;
        }

        // تكييف معدل الاستكشاف
        if (this.advancedAnalytics.explorationEfficiency < 0.1) {
            // الاستكشاف غير فعال - زيادة epsilon
            this.config.epsilon = Math.min(0.5, this.config.epsilon * 1.05);
        }

        // تحديث محسن الشبكة
        await this.updateOptimizer();
    }

    /**
     * تحديث محسن الشبكة
     */
    async updateOptimizer() {
        const newOptimizer = tf.train.adam(this.config.learningRate);

        // إعادة تجميع النموذج مع المحسن الجديد
        this.qNetwork.compile({
            optimizer: newOptimizer,
            loss: 'meanSquaredError',
            metrics: ['mae']
        });
    }

    /**
     * تحسين بنية الشبكة
     */
    async optimizeNetworkArchitecture() {
        // إذا كان الأداء ضعيف، اقترح تحسينات
        if (this.stats.winRate < 0.6 && this.stats.totalEpisodes > 1000) {
            console.log('💡 اقتراح: قد تحتاج لزيادة حجم الشبكة أو تعديل البنية');

            // يمكن إضافة منطق لتعديل البنية تلقائياً
            this.suggestArchitectureImprovements();
        }
    }

    /**
     * اقتراح تحسينات البنية
     */
    suggestArchitectureImprovements() {
        const suggestions = [];

        if (this.stats.stabilityScore < 0.6) {
            suggestions.push('إضافة طبقات Dropout للتنظيم');
            suggestions.push('تقليل معدل التعلم');
        }

        if (this.stats.convergenceRate < 0.001) {
            suggestions.push('زيادة حجم الطبقات المخفية');
            suggestions.push('إضافة طبقات إضافية');
        }

        if (this.advancedAnalytics.explorationEfficiency < 0.1) {
            suggestions.push('تحسين استراتيجية الاستكشاف');
            suggestions.push('استخدام Noisy Networks');
        }

        console.log('💡 اقتراحات التحسين:', suggestions);
        return suggestions;
    }

    /**
     * تحسين سرعة التنفيذ
     */
    async optimizeExecutionSpeed() {
        const startTime = Date.now();

        // تحسين حجم الدفعة بناءً على الذاكرة المتاحة
        const memoryUsage = process.memoryUsage();
        const availableMemory = memoryUsage.heapTotal - memoryUsage.heapUsed;

        if (availableMemory > 100 * 1024 * 1024) { // 100MB
            this.config.batchSize = Math.min(64, this.config.batchSize * 1.2);
        } else if (availableMemory < 50 * 1024 * 1024) { // 50MB
            this.config.batchSize = Math.max(16, this.config.batchSize * 0.8);
        }

        const endTime = Date.now();
        this.stats.decisionLatency = endTime - startTime;
    }

    /**
     * حساب التباين
     */
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }

    /**
     * حساب الميل
     */
    calculateSlope(values) {
        const n = values.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = values.reduce((sum, val) => sum + val, 0);
        const sumXY = values.reduce((sum, val, index) => sum + index * val, 0);
        const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }

    /**
     * إعادة تعيين النموذج
     */
    reset() {
        this.memory = [];
        this.priorities = [];
        this.trainStep = 0;
        this.config.epsilon = 1.0;
        this.stats = {
            totalEpisodes: 0,
            averageReward: 0,
            bestReward: -Infinity,
            winRate: 0,
            explorationRate: this.config.epsilon,
            learningProgress: 0,
            recentPerformance: [],
            convergenceRate: 0,
            stabilityScore: 0,
            adaptabilityIndex: 0,
            decisionLatency: 0,
            accuracyTrend: [],
            lossHistory: [],
            gradientNorm: 0
        };

        this.initializeNetworks();
        console.log('🔄 تم إعادة تعيين النموذج');
    }
}

module.exports = DeepQLearningAgent;
