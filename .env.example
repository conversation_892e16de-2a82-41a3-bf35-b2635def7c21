# ===============================================================================
# ملف التكوين المحدث - FlashBot Master Configuration
# ===============================================================================
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب احتياجاتك
# جميع المتغيرات هنا تُقرأ من نظام التكوين المركزي الجديد في config/master-config.js
# ===============================================================================

# === إعدادات البيئة ===
NODE_ENV=production
DEBUG=false
LOG_LEVEL=info

# === إعدادات API المحلية ===
PORT=3000
HOST=localhost
API_BASE_URL=http://localhost:3000

# === إعدادات عميل API المحسن ===
# مفاتيح API (مطلوبة في الإنتاج)
FLASHBOT_API_KEY=your_secure_api_key_here
API_KEY=your_secure_api_key_here

# URLs للبيئات المختلفة
API_PRODUCTION_URL=https://api.flashbot.com
API_STAGING_URL=https://staging-api.flashbot.com
API_DEV_URL=http://localhost:3000
API_TEST_URL=http://localhost:3001

# إعدادات الاتصال المحسنة
API_TIMEOUT=30000
API_CONNECT_TIMEOUT=5000
API_MAX_RETRIES=3
API_RETRY_DELAY=1000
API_KEEP_ALIVE=true
API_KEEP_ALIVE_MS=1000
API_MAX_SOCKETS=10
API_MAX_FREE_SOCKETS=5

# إعدادات إعادة المحاولة المتقدمة
API_RETRY_ENABLED=true
API_MAX_RETRY_ATTEMPTS=3
API_RETRY_BASE_DELAY=1000
API_RETRY_MAX_DELAY=10000
API_EXPONENTIAL_BACKOFF=true

# إعدادات Circuit Breaker
API_CIRCUIT_BREAKER_ENABLED=true
API_FAILURE_THRESHOLD=5
API_RESET_TIMEOUT=60000
API_MONITORING_PERIOD=10000

# إعدادات Queue للطلبات
API_QUEUE_ENABLED=false
API_QUEUE_MAX_SIZE=100
API_QUEUE_CONCURRENCY=5
API_QUEUE_PRIORITY=true

# إعدادات Rate Limiting من جانب العميل
CLIENT_RATE_LIMIT_ENABLED=true
CLIENT_MAX_RPS=10
CLIENT_MAX_RPM=100
CLIENT_BURST_LIMIT=20

# إعدادات التشفير المتقدم (اختياري)
API_ENCRYPTION_ENABLED=false
API_ENCRYPTION_KEY=your_encryption_key_here

# إعدادات التخزين المؤقت المحسن
API_CACHE_ENABLED=true
API_CACHE_TYPE=memory
API_CACHE_TTL=300000
API_CACHE_TTL_SHORT=60000
API_CACHE_TTL_LONG=3600000
API_CACHE_MAX_SIZE=100
API_CACHE_CHECK_PERIOD=600000
API_CACHE_REDIS_ENABLED=false
API_CACHE_REDIS_URL=redis://localhost:6379
API_CACHE_KEY_PREFIX=flashbot:api:

# إعدادات التسجيل والمراقبة المتقدمة
API_REQUEST_LOGGING=true
API_REQUEST_LOG_LEVEL=info
API_LOG_BODY=false
API_LOG_RESPONSE=false
API_ERROR_LOGGING=true
API_ERROR_LOG_LEVEL=error
API_METRICS_ENABLED=true
API_METRICS_INTERVAL=60000
API_METRICS_HISTORY=100

# إعدادات Health Check
API_HEALTH_CHECK_ENABLED=true
API_HEALTH_CHECK_INTERVAL=30000
API_HEALTH_CHECK_TIMEOUT=5000
API_HEALTH_CHECK_ENDPOINT=/health

# إعدادات التنبيهات والإشعارات
API_ERROR_ALERTS=false
API_ERROR_WEBHOOK_URL=
API_ERROR_EMAIL=
API_ERROR_THRESHOLD=5

# إعدادات Fallback والنسخ الاحتياطية
API_FALLBACK_ENABLED=false
API_FALLBACK_URLS=
API_FALLBACK_STRATEGY=round_robin

# === إعدادات WebSocket ===
WS_ENABLED=true
WS_PORT=3001
WS_PING_INTERVAL=30000

# === مقدمو خدمة RPC عالية الجودة (مطلوبة لـ Failover Provider) ===
# احصل على مفاتيح مجانية من:
# Alchemy: https://www.alchemy.com/
# Infura: https://infura.io/
# QuickNode: https://www.quicknode.com/

# Ethereum Mainnet
ALCHEMY_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY
INFURA_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
QUICKNODE_RPC_URL=https://YOUR_ENDPOINT.quiknode.pro/YOUR_API_KEY/

# === إعدادات Failover Provider ===
FAILOVER_MAX_RETRIES=3
FAILOVER_RETRY_DELAY=1000
FAILOVER_HEALTH_CHECK_INTERVAL=30000
FAILOVER_RATE_LIMIT_DELAY=2000
FAILOVER_TIMEOUT=10000

# === مفاتيح API للتحقق من العقود (مطلوبة) ===
# احصل على مفاتيح API من المواقع الرسمية
ETHERSCAN_API_KEY=your-etherscan-api-key-here

# === مفاتيح API إضافية (اختيارية - ستستخدم ETHERSCAN_API_KEY إذا لم تُحدد) ===
ARBISCAN_API_KEY=your-arbiscan-api-key-here
POLYGONSCAN_API_KEY=your-polygonscan-api-key-here
BSCSCAN_API_KEY=your-bscscan-api-key-here
OPTIMISTIC_ETHERSCAN_API_KEY=your-optimistic-etherscan-api-key-here
BASESCAN_API_KEY=your-basescan-api-key-here

# === مفاتيح API للبيانات ===
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key-here
COINGECKO_API_KEY=your-coingecko-api-key-here

# === إعدادات الشبكات الرئيسية - RPC URLs ===
# Ethereum Mainnet
ETHEREUM_RPC_URL=https://eth.llamarpc.com
ETHEREUM_WS_URL=wss://eth.llamarpc.com

# Arbitrum One
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
ARBITRUM_WS_URL=wss://arb1.arbitrum.io/ws

# Optimism Mainnet
OPTIMISM_RPC_URL=https://mainnet.optimism.io
OPTIMISM_WS_URL=wss://ws-mainnet.optimism.io

# Polygon Mainnet
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_WS_URL=wss://ws-mainnet.matic.network

# Binance Smart Chain
BSC_RPC_URL=https://bsc-dataseed1.binance.org
BSC_WS_URL=wss://bsc-ws-node.nariox.org:443

# Base Mainnet
BASE_RPC_URL=https://mainnet.base.org
BASE_WS_URL=wss://mainnet.base.org

# === إعدادات الشبكات التجريبية (للتطوير والاختبار) ===
# Goerli Testnet
GOERLI_RPC_URL=https://goerli.infura.io/v3/YOUR_PROJECT_ID
GOERLI_WS_URL=wss://goerli.infura.io/ws/v3/YOUR_PROJECT_ID

# Mumbai Testnet (Polygon)
MUMBAI_RPC_URL=https://rpc-mumbai.maticvigil.com
MUMBAI_WS_URL=wss://ws-mumbai.matic.today

# Arbitrum Goerli
ARBITRUM_GOERLI_RPC_URL=https://goerli-rollup.arbitrum.io/rpc
ARBITRUM_GOERLI_WS_URL=wss://goerli-rollup.arbitrum.io/ws

# Legacy support (للتوافق مع الكود القديم)
MAINNET_RPC_URL=https://eth.llamarpc.com

# === إعدادات المحفظة والأمان (مطلوبة) ===
PRIVATE_KEY=your-private-key-here
WALLET_ADDRESS=your-wallet-address-here
ENCRYPTION_KEY=your-encryption-key-here
JWT_SECRET=your-jwt-secret-here

# === إعدادات التداول ===
TRADING_ENABLED=true
MAX_CONCURRENT_TRADES=3
COOLDOWN_PERIOD=5000
MIN_PROFIT_THRESHOLD=0.01
MIN_NET_PROFIT=0.005
MAX_SLIPPAGE=0.03
MAX_POSITION_SIZE=10
MAX_DAILY_LOSS=1
EMERGENCY_STOP_LOSS=0.1

# === إعدادات الغاز ===
GAS_PRICE_MULTIPLIER=1.1
MAX_GAS_PRICE=100
FLASH_LOAN_GAS_LIMIT=500000
ARBITRAGE_GAS_LIMIT=300000
LIQUIDATION_GAS_LIMIT=400000

# === إعدادات المراقبة ===
MONITORING_ENABLED=true
PRICE_CHECK_INTERVAL=5000
LIQUIDATION_CHECK_INTERVAL=10000
HEALTH_CHECK_INTERVAL=30000
METRICS_INTERVAL=60000

# === إعدادات التسجيل ===
LOG_FILE=logs/flashbot.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# === إعدادات التنبيهات ===
ALERT_WEBHOOK_URL=your-webhook-url-here
ALERT_EMAIL=your-alert-email-here
SLACK_WEBHOOK_URL=your-slack-webhook-url-here
DISCORD_WEBHOOK_URL=your-discord-webhook-url-here

# === إعدادات الذكاء الاصطناعي ===
AI_ENABLED=true
AI_CONFIDENCE_THRESHOLD=0.7
AI_UPDATE_INTERVAL=300000
MAX_RISK_SCORE=0.8

# === إعدادات Rate Limiting والأمان ===
RATE_LIMIT_MAX=100
CORS_ORIGIN=http://localhost:3000
TRUST_PROXY=false
JSON_LIMIT=10mb

# === إعدادات قاعدة البيانات (اختيارية) ===
# Redis للتخزين المؤقت
REDIS_ENABLED=false
REDIS_URL=redis://localhost:6379
REDIS_TTL=3600

# MongoDB للبيانات التاريخية
MONGODB_ENABLED=false
MONGODB_URL=mongodb://localhost:27017/flashbot

# === إعدادات التقارير ===
REPORT_GAS=true
DEPLOYMENT_WEBHOOK_URL=your-deployment-webhook-url-here

# === الشبكات المفعلة ===
ENABLED_NETWORKS=ethereum,arbitrum,polygon,bsc,optimism,base

# ===============================================================================
# ملاحظات مهمة:
# ===============================================================================
# 1. جميع هذه المتغيرات تُقرأ من config/master-config.js
# 2. يمكن ترك المتغيرات الاختيارية فارغة - ستُستخدم القيم الافتراضية
# 3. تأكد من حماية ملف .env ولا تشاركه مع أحد
# 4. للنشر التجريبي، استخدم .env.testnet كمرجع
# 5. للإنتاج، تأكد من استخدام مفاتيح وعناوين حقيقية
# ===============================================================================
