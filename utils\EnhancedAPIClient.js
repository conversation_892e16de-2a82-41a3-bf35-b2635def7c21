/**
 * @title Enhanced API Client - عميل API محسن
 * @description عميل API متقدم مع أفضل الممارسات الأمنية وإدارة الأخطاء
 * <AUTHOR> Team
 * @version 2.0.0
 */

const axios = require('axios');
const https = require('https');
const crypto = require('crypto');
const EventEmitter = require('events');
const { getAPIClientConfig } = require('../config/api-client-config');

/**
 * عميل API محسن مع ميزات متقدمة
 */
class EnhancedAPIClient extends EventEmitter {
    constructor(customConfig = {}) {
        super();
        
        // دمج التكوين المخصص مع التكوين الافتراضي
        this.config = { ...getAPIClientConfig(), ...customConfig };
        
        // إحصائيات الأداء
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            lastRequestTime: null,
            circuitBreakerState: 'CLOSED' // CLOSED, OPEN, HALF_OPEN
        };
        
        // ذاكرة التخزين المؤقت
        this.cache = new Map();
        
        // قائمة انتظار الطلبات
        this.requestQueue = [];
        this.activeRequests = 0;
        
        // Circuit Breaker
        this.circuitBreaker = {
            failures: 0,
            lastFailureTime: null,
            state: 'CLOSED'
        };
        
        // Rate Limiter
        this.rateLimiter = {
            requests: [],
            lastReset: Date.now()
        };
        
        // إنشاء عميل Axios محسن
        this.createAxiosInstance();
        
        // بدء المراقبة
        this.startMonitoring();
        
        console.log('🚀 تم تهيئة عميل API محسن');
    }
    
    /**
     * إنشاء instance محسن من Axios
     */
    createAxiosInstance() {
        const { connection, security } = this.config;
        
        // إعداد HTTPS Agent محسن
        const httpsAgent = new https.Agent({
            ...security.httpsAgent,
            timeout: connection.connectTimeout
        });
        
        // إنشاء عميل Axios
        this.axiosInstance = axios.create({
            baseURL: connection.baseURL,
            timeout: connection.timeout,
            httpsAgent,
            
            // Headers افتراضية آمنة
            headers: {
                ...security.securityHeaders,
                'X-API-Key': security.apiKey
            },
            
            // إعدادات إضافية
            maxRedirects: 3,
            validateStatus: (status) => status < 500, // لا نعتبر 4xx كأخطاء
        });
        
        // إضافة JWT Token إذا كان متوفراً
        if (security.jwtToken) {
            this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${security.jwtToken}`;
        }
        
        // إعداد Interceptors
        this.setupInterceptors();
    }
    
    /**
     * إعداد Interceptors للطلبات والاستجابات
     */
    setupInterceptors() {
        // Request Interceptor
        this.axiosInstance.interceptors.request.use(
            (config) => this.handleRequest(config),
            (error) => this.handleRequestError(error)
        );
        
        // Response Interceptor
        this.axiosInstance.interceptors.response.use(
            (response) => this.handleResponse(response),
            (error) => this.handleResponseError(error)
        );
    }
    
    /**
     * معالج الطلبات الصادرة
     */
    async handleRequest(config) {
        // فحص Circuit Breaker
        if (this.circuitBreaker.state === 'OPEN') {
            throw new Error('Circuit Breaker مفتوح - الخدمة غير متاحة مؤقتاً');
        }
        
        // فحص Rate Limiting
        if (!this.checkRateLimit()) {
            throw new Error('تم تجاوز حد الطلبات المسموح');
        }
        
        // إضافة معرف فريد للطلب
        config.metadata = {
            requestId: this.generateRequestId(),
            startTime: Date.now()
        };
        
        // تشفير البيانات إذا كان مفعلاً
        if (this.config.security.encryption.enabled && config.data) {
            config.data = this.encryptData(config.data);
            config.headers['X-Encrypted'] = 'true';
        }
        
        // تسجيل الطلب
        this.logRequest(config);
        
        return config;
    }
    
    /**
     * معالج الاستجابات الواردة
     */
    async handleResponse(response) {
        const { config } = response;
        const duration = Date.now() - config.metadata.startTime;
        
        // تحديث الإحصائيات
        this.updateStats(true, duration);
        
        // إعادة تعيين Circuit Breaker عند النجاح
        this.resetCircuitBreaker();
        
        // فك تشفير البيانات إذا لزم الأمر
        if (response.headers['x-encrypted'] === 'true') {
            response.data = this.decryptData(response.data);
        }
        
        // حفظ في التخزين المؤقت إذا كان مناسباً
        if (this.shouldCache(config)) {
            this.cacheResponse(config, response.data);
        }
        
        // تسجيل الاستجابة
        this.logResponse(response, duration);
        
        // إرسال حدث نجاح
        this.emit('requestSuccess', {
            requestId: config.metadata.requestId,
            method: config.method,
            url: config.url,
            duration,
            status: response.status
        });
        
        return response;
    }
    
    /**
     * معالج أخطاء الطلبات
     */
    async handleRequestError(error) {
        this.logError('Request Error', error);
        this.emit('requestError', { type: 'request', error });
        return Promise.reject(error);
    }
    
    /**
     * معالج أخطاء الاستجابات
     */
    async handleResponseError(error) {
        const { config } = error;
        const duration = config?.metadata ? Date.now() - config.metadata.startTime : 0;
        
        // تحديث الإحصائيات
        this.updateStats(false, duration);
        
        // تحديث Circuit Breaker
        this.updateCircuitBreaker();
        
        // تسجيل الخطأ
        this.logError('Response Error', error, duration);
        
        // إرسال حدث خطأ
        this.emit('requestError', {
            requestId: config?.metadata?.requestId,
            type: 'response',
            error,
            status: error.response?.status,
            duration
        });
        
        // محاولة إعادة الطلب إذا كان مناسباً
        if (this.shouldRetry(error)) {
            return this.retryRequest(error);
        }
        
        // محاولة استخدام Fallback
        if (this.config.errorHandling.fallback.enabled) {
            return this.tryFallback(error);
        }
        
        return Promise.reject(this.enhanceError(error));
    }
    
    /**
     * طلب GET محسن
     */
    async get(url, config = {}) {
        // فحص التخزين المؤقت أولاً
        const cacheKey = this.generateCacheKey('GET', url, config.params);
        const cachedResponse = this.getFromCache(cacheKey);
        
        if (cachedResponse) {
            this.emit('cacheHit', { url, cacheKey });
            return { data: cachedResponse, fromCache: true };
        }
        
        return this.makeRequest('get', url, { ...config, cacheKey });
    }
    
    /**
     * طلب POST محسن
     */
    async post(url, data, config = {}) {
        return this.makeRequest('post', url, { ...config, data });
    }
    
    /**
     * طلب PUT محسن
     */
    async put(url, data, config = {}) {
        return this.makeRequest('put', url, { ...config, data });
    }
    
    /**
     * طلب DELETE محسن
     */
    async delete(url, config = {}) {
        return this.makeRequest('delete', url, config);
    }
    
    /**
     * تنفيذ طلب مع إدارة Queue
     */
    async makeRequest(method, url, config = {}) {
        // إضافة للقائمة إذا كان Queue مفعلاً
        if (this.config.requestManagement.requestQueue.enabled) {
            return this.queueRequest(method, url, config);
        }
        
        return this.executeRequest(method, url, config);
    }
    
    /**
     * تنفيذ الطلب الفعلي
     */
    async executeRequest(method, url, config = {}) {
        this.activeRequests++;
        
        try {
            const response = await this.axiosInstance[method](url, config.data, config);
            return response;
        } finally {
            this.activeRequests--;
        }
    }
    
    /**
     * إضافة طلب لقائمة الانتظار
     */
    async queueRequest(method, url, config = {}) {
        return new Promise((resolve, reject) => {
            const queueConfig = this.config.requestManagement.requestQueue;
            
            // فحص حجم القائمة
            if (this.requestQueue.length >= queueConfig.maxSize) {
                reject(new Error('قائمة انتظار الطلبات ممتلئة'));
                return;
            }
            
            // إضافة للقائمة
            this.requestQueue.push({
                method,
                url,
                config,
                resolve,
                reject,
                priority: config.priority || 0,
                timestamp: Date.now()
            });
            
            // معالجة القائمة
            this.processQueue();
        });
    }
    
    /**
     * معالجة قائمة انتظار الطلبات
     */
    async processQueue() {
        const queueConfig = this.config.requestManagement.requestQueue;
        
        // فحص إذا كان يمكن معالجة طلبات إضافية
        if (this.activeRequests >= queueConfig.concurrency || this.requestQueue.length === 0) {
            return;
        }
        
        // ترتيب حسب الأولوية إذا كان مفعلاً
        if (queueConfig.priority) {
            this.requestQueue.sort((a, b) => b.priority - a.priority);
        }
        
        // معالجة الطلب التالي
        const request = this.requestQueue.shift();
        
        try {
            const response = await this.executeRequest(request.method, request.url, request.config);
            request.resolve(response);
        } catch (error) {
            request.reject(error);
        }
        
        // معالجة الطلب التالي
        setTimeout(() => this.processQueue(), 10);
    }
    
    /**
     * فحص حدود الطلبات (Rate Limiting)
     */
    checkRateLimit() {
        const { rateLimiting } = this.config.security;
        
        if (!rateLimiting.enabled) return true;
        
        const now = Date.now();
        
        // تنظيف الطلبات القديمة
        this.rateLimiter.requests = this.rateLimiter.requests.filter(
            time => now - time < 60000 // آخر دقيقة
        );
        
        // فحص حد الطلبات في الدقيقة
        if (this.rateLimiter.requests.length >= rateLimiting.maxRequestsPerMinute) {
            return false;
        }
        
        // فحص حد الطلبات في الثانية
        const recentRequests = this.rateLimiter.requests.filter(
            time => now - time < 1000 // آخر ثانية
        );
        
        if (recentRequests.length >= rateLimiting.maxRequestsPerSecond) {
            return false;
        }
        
        // إضافة الطلب الحالي
        this.rateLimiter.requests.push(now);
        
        return true;
    }
    
    /**
     * فحص ما إذا كان يجب إعادة المحاولة
     */
    shouldRetry(error) {
        const { retry } = this.config.requestManagement;
        
        if (!retry.enabled) return false;
        
        // فحص عدد المحاولات
        const retryCount = error.config?.retryCount || 0;
        if (retryCount >= retry.maxAttempts) return false;
        
        // فحص شروط إعادة المحاولة
        return retry.retryCondition(error);
    }
    
    /**
     * إعادة محاولة الطلب
     */
    async retryRequest(error) {
        const { retry } = this.config.requestManagement;
        const retryCount = (error.config.retryCount || 0) + 1;
        
        // حساب فترة الانتظار
        let delay = retry.baseDelay;
        if (retry.exponentialBackoff) {
            delay = Math.min(retry.baseDelay * Math.pow(2, retryCount - 1), retry.maxDelay);
        }
        
        // انتظار قبل إعادة المحاولة
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // تحديث تكوين الطلب
        error.config.retryCount = retryCount;
        
        console.log(`🔄 إعادة محاولة ${retryCount}/${retry.maxAttempts} للطلب ${error.config.url}`);
        
        return this.axiosInstance.request(error.config);
    }
    
    /**
     * محاولة استخدام نقطة نهاية بديلة
     */
    async tryFallback(error) {
        const { fallback } = this.config.errorHandling;
        const fallbackEndpoints = fallback.endpoints;
        
        if (fallbackEndpoints.length === 0) {
            return Promise.reject(error);
        }
        
        console.log('🔄 محاولة استخدام نقطة نهاية بديلة...');
        
        // تجربة كل نقطة نهاية بديلة
        for (const endpoint of fallbackEndpoints) {
            try {
                const originalBaseURL = this.axiosInstance.defaults.baseURL;
                this.axiosInstance.defaults.baseURL = endpoint;
                
                const response = await this.axiosInstance.request(error.config);
                
                // استعادة URL الأصلي
                this.axiosInstance.defaults.baseURL = originalBaseURL;
                
                console.log(`✅ نجح الطلب باستخدام نقطة النهاية البديلة: ${endpoint}`);
                return response;
                
            } catch (fallbackError) {
                console.log(`❌ فشل الطلب في نقطة النهاية البديلة: ${endpoint}`);
                continue;
            }
        }
        
        return Promise.reject(error);
    }
    
    /**
     * تحسين رسالة الخطأ
     */
    enhanceError(error) {
        const enhancedError = new Error();
        
        // معلومات أساسية
        enhancedError.message = this.getErrorMessage(error);
        enhancedError.code = error.code || 'UNKNOWN_ERROR';
        enhancedError.status = error.response?.status;
        enhancedError.statusText = error.response?.statusText;
        
        // معلومات الطلب
        enhancedError.request = {
            method: error.config?.method,
            url: error.config?.url,
            baseURL: error.config?.baseURL
        };
        
        // معلومات الاستجابة (إذا كانت متوفرة)
        if (error.response) {
            enhancedError.response = {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers,
                data: error.response.data
            };
        }
        
        // معلومات إضافية
        enhancedError.timestamp = new Date().toISOString();
        enhancedError.requestId = error.config?.metadata?.requestId;
        
        return enhancedError;
    }
    
    /**
     * الحصول على رسالة خطأ واضحة
     */
    getErrorMessage(error) {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            // رسائل خطأ مخصصة حسب رمز الحالة
            switch (status) {
                case 400:
                    return `طلب غير صحيح: ${data?.message || 'البيانات المرسلة غير صحيحة'}`;
                case 401:
                    return 'غير مصرح: مفتاح API غير صحيح أو منتهي الصلاحية';
                case 403:
                    return 'ممنوع: ليس لديك صلاحية للوصول لهذا المورد';
                case 404:
                    return `غير موجود: ${error.config?.url} غير متاح`;
                case 429:
                    return 'تم تجاوز حد الطلبات: يرجى المحاولة لاحقاً';
                case 500:
                    return 'خطأ داخلي في الخادم: يرجى المحاولة لاحقاً';
                case 502:
                    return 'خطأ في البوابة: الخادم غير متاح مؤقتاً';
                case 503:
                    return 'الخدمة غير متاحة: الخادم في صيانة';
                case 504:
                    return 'انتهت مهلة البوابة: الخادم لم يستجب في الوقت المحدد';
                default:
                    return data?.message || `خطأ HTTP ${status}: ${error.response.statusText}`;
            }
        } else if (error.request) {
            return 'خطأ في الشبكة: لا يمكن الوصول للخادم';
        } else {
            return error.message || 'خطأ غير معروف';
        }
    }
    
    // ==================== وظائف مساعدة ====================
    
    generateRequestId() {
        return crypto.randomBytes(8).toString('hex');
    }
    
    generateCacheKey(method, url, params) {
        const key = `${method}:${url}:${JSON.stringify(params || {})}`;
        return crypto.createHash('md5').update(key).digest('hex');
    }
    
    shouldCache(config) {
        return this.config.caching.enabled && 
               config.method === 'get' && 
               !config.headers['X-No-Cache'];
    }
    
    cacheResponse(config, data) {
        if (!this.config.caching.enabled) return;
        
        const cacheKey = config.cacheKey;
        const ttl = config.cacheTTL || this.config.caching.ttl.default;
        
        this.cache.set(cacheKey, {
            data,
            timestamp: Date.now(),
            ttl
        });
        
        // تنظيف التخزين المؤقت إذا تجاوز الحد الأقصى
        if (this.cache.size > this.config.caching.memory.maxSize) {
            this.cleanupCache();
        }
    }
    
    getFromCache(cacheKey) {
        if (!this.config.caching.enabled) return null;
        
        const cached = this.cache.get(cacheKey);
        if (!cached) return null;
        
        // فحص انتهاء الصلاحية
        if (Date.now() - cached.timestamp > cached.ttl) {
            this.cache.delete(cacheKey);
            return null;
        }
        
        return cached.data;
    }
    
    cleanupCache() {
        const entries = Array.from(this.cache.entries());
        const now = Date.now();
        
        // حذف العناصر المنتهية الصلاحية
        entries.forEach(([key, value]) => {
            if (now - value.timestamp > value.ttl) {
                this.cache.delete(key);
            }
        });
        
        // حذف أقدم العناصر إذا لزم الأمر
        if (this.cache.size > this.config.caching.memory.maxSize) {
            const sortedEntries = entries
                .sort((a, b) => a[1].timestamp - b[1].timestamp)
                .slice(0, this.cache.size - this.config.caching.memory.maxSize);
            
            sortedEntries.forEach(([key]) => this.cache.delete(key));
        }
    }
    
    encryptData(data) {
        if (!this.config.security.encryption.enabled) return data;
        
        const { algorithm, key } = this.config.security.encryption;
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key);
        
        let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        return {
            encrypted,
            iv: iv.toString('hex'),
            algorithm
        };
    }
    
    decryptData(encryptedData) {
        if (!this.config.security.encryption.enabled || !encryptedData.encrypted) {
            return encryptedData;
        }
        
        const { algorithm, key } = this.config.security.encryption;
        const decipher = crypto.createDecipher(algorithm, key);
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return JSON.parse(decrypted);
    }
    
    updateStats(success, duration) {
        this.stats.totalRequests++;
        this.stats.lastRequestTime = Date.now();
        
        if (success) {
            this.stats.successfulRequests++;
        } else {
            this.stats.failedRequests++;
        }
        
        // تحديث متوسط وقت الاستجابة
        this.stats.averageResponseTime = 
            (this.stats.averageResponseTime + duration) / 2;
    }
    
    updateCircuitBreaker() {
        const { circuitBreaker: config } = this.config.requestManagement;
        
        if (!config.enabled) return;
        
        this.circuitBreaker.failures++;
        this.circuitBreaker.lastFailureTime = Date.now();
        
        // فتح Circuit Breaker إذا تجاوز عدد الأخطاء الحد المسموح
        if (this.circuitBreaker.failures >= config.failureThreshold) {
            this.circuitBreaker.state = 'OPEN';
            this.stats.circuitBreakerState = 'OPEN';
            
            console.log('⚠️ Circuit Breaker مفتوح - تم تجاوز حد الأخطاء');
            
            // إعادة تعيين بعد فترة
            setTimeout(() => {
                this.circuitBreaker.state = 'HALF_OPEN';
                this.stats.circuitBreakerState = 'HALF_OPEN';
                console.log('🔄 Circuit Breaker في حالة نصف مفتوح');
            }, config.resetTimeout);
        }
    }
    
    resetCircuitBreaker() {
        if (this.circuitBreaker.state === 'HALF_OPEN') {
            this.circuitBreaker.state = 'CLOSED';
            this.circuitBreaker.failures = 0;
            this.stats.circuitBreakerState = 'CLOSED';
            console.log('✅ Circuit Breaker مغلق - تم استعادة الخدمة');
        }
    }
    
    logRequest(config) {
        const { requestLogging } = this.config.monitoring;
        
        if (!requestLogging.enabled) return;
        
        const logData = {
            requestId: config.metadata.requestId,
            method: config.method?.toUpperCase(),
            url: config.url,
            timestamp: new Date().toISOString()
        };
        
        if (requestLogging.includeHeaders) {
            logData.headers = this.sanitizeHeaders(config.headers);
        }
        
        if (requestLogging.includeBody && config.data) {
            logData.body = config.data;
        }
        
        console.log(`📤 [${logData.requestId}] ${logData.method} ${logData.url}`);
    }
    
    logResponse(response, duration) {
        const { requestLogging } = this.config.monitoring;
        
        if (!requestLogging.enabled) return;
        
        const { config } = response;
        const logData = {
            requestId: config.metadata.requestId,
            status: response.status,
            duration: `${duration}ms`,
            timestamp: new Date().toISOString()
        };
        
        console.log(`📥 [${logData.requestId}] ${logData.status} (${logData.duration})`);
    }
    
    logError(type, error, duration = 0) {
        const { logging } = this.config.errorHandling;
        
        if (!logging.enabled) return;
        
        const logData = {
            type,
            message: error.message,
            status: error.response?.status,
            duration: duration ? `${duration}ms` : undefined,
            timestamp: new Date().toISOString()
        };
        
        if (logging.includeStack && error.stack) {
            logData.stack = error.stack;
        }
        
        console.error(`❌ [${error.config?.metadata?.requestId || 'unknown'}] ${logData.message}`);
    }
    
    sanitizeHeaders(headers) {
        const { sanitizeHeaders } = this.config.monitoring.requestLogging;
        const sanitized = { ...headers };
        
        sanitizeHeaders.forEach(header => {
            if (sanitized[header]) {
                sanitized[header] = '***';
            }
        });
        
        return sanitized;
    }
    
    startMonitoring() {
        const { metrics } = this.config.monitoring;
        
        if (!metrics.enabled) return;
        
        // تنظيف دوري للتخزين المؤقت
        setInterval(() => {
            this.cleanupCache();
        }, this.config.caching.memory.checkPeriod);
        
        // إرسال مقاييس الأداء دورياً
        setInterval(() => {
            this.emit('metrics', this.getMetrics());
        }, metrics.collectInterval);
    }
    
    getMetrics() {
        return {
            ...this.stats,
            cache: {
                size: this.cache.size,
                hitRate: this.calculateCacheHitRate()
            },
            queue: {
                size: this.requestQueue.length,
                activeRequests: this.activeRequests
            },
            timestamp: Date.now()
        };
    }
    
    calculateCacheHitRate() {
        // حساب معدل نجاح التخزين المؤقت
        // يمكن تحسين هذا بحفظ إحصائيات أكثر تفصيلاً
        return this.cache.size > 0 ? 0.8 : 0; // قيمة تقديرية
    }
    
    /**
     * إيقاف العميل وتنظيف الموارد
     */
    async shutdown() {
        console.log('⏹️ إيقاف عميل API...');
        
        // انتظار انتهاء الطلبات النشطة
        while (this.activeRequests > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // تنظيف التخزين المؤقت
        this.cache.clear();
        
        // تنظيف قائمة الانتظار
        this.requestQueue.length = 0;
        
        console.log('✅ تم إيقاف عميل API');
    }
}

module.exports = EnhancedAPIClient;
