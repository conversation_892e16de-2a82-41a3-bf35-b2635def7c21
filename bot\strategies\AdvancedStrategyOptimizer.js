/**
 * @title Advanced Strategy Optimizer - محسن الاستراتيجيات المتقدم
 * @description نظام شامل لتحسين وتطوير الاستراتيجيات السبع
 * <AUTHOR> Team
 */

const { ethers } = require('ethers');
const EventEmitter = require('events');

class AdvancedStrategyOptimizer extends EventEmitter {
    constructor(config = {}) {
        super();
        
        this.config = {
            // إعدادات التحسين العامة
            optimizationInterval: config.optimizationInterval || 300000, // 5 دقائق
            performanceWindow: config.performanceWindow || 86400000, // 24 ساعة
            minDataPoints: config.minDataPoints || 50,
            
            // عتبات الأداء
            minSuccessRate: config.minSuccessRate || 0.7, // 70%
            minProfitMargin: config.minProfitMargin || 0.005, // 0.5%
            maxDrawdown: config.maxDrawdown || 0.1, // 10%
            
            // إعدادات التكيف
            adaptiveLearning: config.adaptiveLearning || true,
            realTimeOptimization: config.realTimeOptimization || true,
            marketRegimeDetection: config.marketRegimeDetection || true,
            
            ...config
        };
        
        // الاستراتيجيات السبع المدعومة
        this.strategies = {
            ARBITRAGE: 'arbitrage',
            LIQUIDATION: 'liquidation', 
            YIELD_FARMING: 'yield_farming',
            OPTIONS_ARBITRAGE: 'options_arbitrage',
            CROSS_CHAIN: 'cross_chain',
            AI_DRIVEN: 'ai_driven',
            MEV_EXTRACTION: 'mev_extraction'
        };
        
        // بيانات الأداء لكل استراتيجية
        this.performanceData = new Map();
        
        // معاملات التحسين لكل استراتيجية
        this.optimizationParams = new Map();
        
        // نتائج التحسين
        this.optimizationResults = new Map();
        
        // حالة السوق
        this.marketState = {
            volatility: 'normal',
            trend: 'neutral',
            liquidity: 'high',
            gasPrice: 'normal',
            lastUpdate: Date.now()
        };
        
        this.initializeStrategies();
        this.startOptimization();
    }
    
    /**
     * تهيئة الاستراتيجيات
     */
    initializeStrategies() {
        Object.values(this.strategies).forEach(strategy => {
            this.performanceData.set(strategy, {
                trades: [],
                metrics: {
                    totalTrades: 0,
                    successfulTrades: 0,
                    totalProfit: 0,
                    totalLoss: 0,
                    averageProfit: 0,
                    successRate: 0,
                    sharpeRatio: 0,
                    maxDrawdown: 0,
                    averageExecutionTime: 0,
                    gasEfficiency: 0
                },
                lastOptimization: Date.now()
            });
            
            this.optimizationParams.set(strategy, this.getDefaultParams(strategy));
        });
        
        console.log('🎯 تم تهيئة محسن الاستراتيجيات المتقدم');
    }
    
    /**
     * الحصول على المعاملات الافتراضية لكل استراتيجية
     */
    getDefaultParams(strategy) {
        const baseParams = {
            minProfitBps: 50,
            maxSlippageBps: 100,
            maxGasPrice: 100,
            riskLevel: 5,
            confidenceThreshold: 0.7,
            executionTimeout: 300
        };
        
        switch (strategy) {
            case this.strategies.ARBITRAGE:
                return {
                    ...baseParams,
                    minProfitBps: 30,
                    maxSlippageBps: 50,
                    priceImpactThreshold: 0.02,
                    liquidityThreshold: 100000
                };
                
            case this.strategies.LIQUIDATION:
                return {
                    ...baseParams,
                    minProfitBps: 100,
                    healthFactorThreshold: 1.05,
                    liquidationBonus: 0.05,
                    maxLiquidationAmount: 1000000
                };
                
            case this.strategies.YIELD_FARMING:
                return {
                    ...baseParams,
                    minAPY: 0.1,
                    maxImpermanentLoss: 0.05,
                    rebalanceThreshold: 0.02,
                    compoundFrequency: 86400
                };
                
            case this.strategies.OPTIONS_ARBITRAGE:
                return {
                    ...baseParams,
                    minProfitBps: 75,
                    maxTimeToExpiry: 30,
                    minImpliedVolatility: 0.2,
                    maxImpliedVolatility: 2.0
                };
                
            case this.strategies.CROSS_CHAIN:
                return {
                    ...baseParams,
                    minProfitBps: 150,
                    maxBridgeFee: 0.01,
                    confirmationBlocks: 12,
                    maxBridgeTime: 1800
                };
                
            case this.strategies.AI_DRIVEN:
                return {
                    ...baseParams,
                    minProfitBps: 25,
                    aiConfidenceThreshold: 0.8,
                    modelUpdateFrequency: 3600,
                    ensembleWeights: [0.3, 0.3, 0.2, 0.2]
                };
                
            case this.strategies.MEV_EXTRACTION:
                return {
                    ...baseParams,
                    minProfitBps: 200,
                    maxMEVCompetition: 0.5,
                    bundleTimeout: 12,
                    priorityFeeMultiplier: 1.5
                };
                
            default:
                return baseParams;
        }
    }
    
    /**
     * بدء عملية التحسين
     */
    startOptimization() {
        // تحسين دوري
        this.optimizationTimer = setInterval(() => {
            this.performOptimization();
        }, this.config.optimizationInterval);
        
        // تحديث حالة السوق
        this.marketTimer = setInterval(() => {
            this.updateMarketState();
        }, 60000); // كل دقيقة
        
        console.log('🔄 بدأ التحسين التلقائي للاستراتيجيات');
    }
    
    /**
     * تحسين جميع الاستراتيجيات
     */
    async performOptimization() {
        console.log('🎯 بدء دورة تحسين الاستراتيجيات...');
        
        try {
            // تحديث حالة السوق
            await this.updateMarketState();
            
            // تحسين كل استراتيجية
            for (const strategy of Object.values(this.strategies)) {
                await this.optimizeStrategy(strategy);
            }
            
            // تحليل الأداء الإجمالي
            await this.analyzeOverallPerformance();
            
            // إنتاج التوصيات
            await this.generateRecommendations();
            
            this.emit('optimizationComplete', {
                timestamp: Date.now(),
                strategies: Object.values(this.strategies),
                marketState: this.marketState
            });
            
        } catch (error) {
            console.error('خطأ في تحسين الاستراتيجيات:', error);
            this.emit('optimizationError', error);
        }
    }
    
    /**
     * تحسين استراتيجية محددة
     */
    async optimizeStrategy(strategy) {
        const performanceData = this.performanceData.get(strategy);
        const currentParams = this.optimizationParams.get(strategy);
        
        if (!performanceData || performanceData.trades.length < this.config.minDataPoints) {
            console.log(`⏳ بيانات غير كافية لتحسين ${strategy}`);
            return;
        }
        
        console.log(`🔧 تحسين استراتيجية ${strategy}...`);
        
        // تحليل الأداء الحالي
        const currentMetrics = this.calculateMetrics(performanceData.trades);
        
        // تحديد نقاط التحسين
        const improvementAreas = this.identifyImprovementAreas(strategy, currentMetrics);
        
        // تطبيق التحسينات
        const optimizedParams = await this.applyOptimizations(strategy, currentParams, improvementAreas);
        
        // اختبار المعاملات الجديدة
        const backtestResults = await this.backtestStrategy(strategy, optimizedParams);
        
        // تحديث المعاملات إذا كانت النتائج أفضل
        if (backtestResults.improvement > 0.05) { // تحسن 5% على الأقل
            this.optimizationParams.set(strategy, optimizedParams);
            
            this.optimizationResults.set(strategy, {
                timestamp: Date.now(),
                oldParams: currentParams,
                newParams: optimizedParams,
                improvement: backtestResults.improvement,
                metrics: backtestResults.metrics
            });
            
            console.log(`✅ تم تحسين ${strategy} بنسبة ${(backtestResults.improvement * 100).toFixed(2)}%`);
        }
    }
    
    /**
     * حساب مقاييس الأداء
     */
    calculateMetrics(trades) {
        if (trades.length === 0) {
            return {
                totalTrades: 0,
                successfulTrades: 0,
                successRate: 0,
                totalProfit: 0,
                averageProfit: 0,
                sharpeRatio: 0,
                maxDrawdown: 0,
                averageExecutionTime: 0,
                gasEfficiency: 0
            };
        }
        
        const successfulTrades = trades.filter(t => t.profit > 0);
        const totalProfit = trades.reduce((sum, t) => sum + t.profit, 0);
        const profits = trades.map(t => t.profit);
        
        // حساب Sharpe Ratio
        const avgProfit = totalProfit / trades.length;
        const variance = profits.reduce((sum, p) => sum + Math.pow(p - avgProfit, 2), 0) / trades.length;
        const stdDev = Math.sqrt(variance);
        const sharpeRatio = stdDev > 0 ? avgProfit / stdDev : 0;
        
        // حساب Max Drawdown
        let maxDrawdown = 0;
        let peak = 0;
        let runningProfit = 0;
        
        for (const trade of trades) {
            runningProfit += trade.profit;
            if (runningProfit > peak) {
                peak = runningProfit;
            }
            const drawdown = (peak - runningProfit) / peak;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }
        }
        
        return {
            totalTrades: trades.length,
            successfulTrades: successfulTrades.length,
            successRate: successfulTrades.length / trades.length,
            totalProfit,
            averageProfit: avgProfit,
            sharpeRatio,
            maxDrawdown,
            averageExecutionTime: trades.reduce((sum, t) => sum + (t.executionTime || 0), 0) / trades.length,
            gasEfficiency: trades.reduce((sum, t) => sum + (t.gasEfficiency || 0), 0) / trades.length
        };
    }
    
    /**
     * تحديد مجالات التحسين
     */
    identifyImprovementAreas(strategy, metrics) {
        const areas = [];
        
        // معدل النجاح منخفض
        if (metrics.successRate < this.config.minSuccessRate) {
            areas.push({
                area: 'success_rate',
                severity: 'high',
                currentValue: metrics.successRate,
                targetValue: this.config.minSuccessRate,
                suggestions: ['تشديد معايير الدخول', 'تحسين توقيت التنفيذ', 'زيادة عتبة الربح']
            });
        }
        
        // هامش الربح منخفض
        if (metrics.averageProfit < this.config.minProfitMargin) {
            areas.push({
                area: 'profit_margin',
                severity: 'medium',
                currentValue: metrics.averageProfit,
                targetValue: this.config.minProfitMargin,
                suggestions: ['تحسين تحسين الغاز', 'البحث عن فرص أكبر', 'تقليل الانزلاق']
            });
        }
        
        // انخفاض أقصى مرتفع
        if (metrics.maxDrawdown > this.config.maxDrawdown) {
            areas.push({
                area: 'risk_management',
                severity: 'high',
                currentValue: metrics.maxDrawdown,
                targetValue: this.config.maxDrawdown,
                suggestions: ['تحسين إدارة المخاطر', 'تقليل حجم المراكز', 'إضافة stop-loss']
            });
        }
        
        // كفاءة الغاز منخفضة
        if (metrics.gasEfficiency < 0.8) {
            areas.push({
                area: 'gas_efficiency',
                severity: 'medium',
                currentValue: metrics.gasEfficiency,
                targetValue: 0.9,
                suggestions: ['تحسين الغاز', 'batch operations', 'استخدام gas tokens']
            });
        }
        
        return areas;
    }
    
    /**
     * تطبيق التحسينات
     */
    async applyOptimizations(strategy, currentParams, improvementAreas) {
        const optimizedParams = { ...currentParams };
        
        for (const area of improvementAreas) {
            switch (area.area) {
                case 'success_rate':
                    // زيادة عتبة الربح لتحسين معدل النجاح
                    optimizedParams.minProfitBps = Math.min(
                        optimizedParams.minProfitBps * 1.2,
                        200
                    );
                    break;
                    
                case 'profit_margin':
                    // تقليل الانزلاق المسموح
                    optimizedParams.maxSlippageBps = Math.max(
                        optimizedParams.maxSlippageBps * 0.8,
                        25
                    );
                    break;
                    
                case 'risk_management':
                    // تقليل مستوى المخاطر
                    optimizedParams.riskLevel = Math.max(
                        optimizedParams.riskLevel - 1,
                        1
                    );
                    break;
                    
                case 'gas_efficiency':
                    // تقليل الحد الأقصى لسعر الغاز
                    optimizedParams.maxGasPrice = Math.max(
                        optimizedParams.maxGasPrice * 0.9,
                        50
                    );
                    break;
            }
        }
        
        // تطبيق تحسينات خاصة بالاستراتيجية
        return this.applyStrategySpecificOptimizations(strategy, optimizedParams);
    }
    
    /**
     * تطبيق تحسينات خاصة بكل استراتيجية
     */
    applyStrategySpecificOptimizations(strategy, params) {
        switch (strategy) {
            case this.strategies.ARBITRAGE:
                // تحسين عتبة تأثير السعر
                if (this.marketState.volatility === 'high') {
                    params.priceImpactThreshold *= 1.5;
                }
                break;
                
            case this.strategies.LIQUIDATION:
                // تعديل عتبة Health Factor حسب السوق
                if (this.marketState.volatility === 'high') {
                    params.healthFactorThreshold = 1.1;
                } else {
                    params.healthFactorThreshold = 1.03;
                }
                break;
                
            case this.strategies.YIELD_FARMING:
                // تعديل تكرار إعادة التوازن
                if (this.marketState.volatility === 'high') {
                    params.compoundFrequency = 43200; // 12 ساعة
                } else {
                    params.compoundFrequency = 86400; // 24 ساعة
                }
                break;
                
            case this.strategies.CROSS_CHAIN:
                // تعديل رسوم الجسر المقبولة
                if (this.marketState.gasPrice === 'high') {
                    params.maxBridgeFee *= 1.5;
                }
                break;
        }
        
        return params;
    }
    
    /**
     * اختبار الاستراتيجية بالمعاملات الجديدة
     */
    async backtestStrategy(strategy, newParams) {
        const performanceData = this.performanceData.get(strategy);
        const trades = performanceData.trades.slice(-100); // آخر 100 صفقة
        
        // محاكاة الأداء بالمعاملات الجديدة
        const simulatedTrades = trades.map(trade => {
            return this.simulateTradeWithNewParams(trade, newParams);
        });
        
        const newMetrics = this.calculateMetrics(simulatedTrades);
        const oldMetrics = this.calculateMetrics(trades);
        
        // حساب التحسن
        const improvement = this.calculateImprovement(oldMetrics, newMetrics);
        
        return {
            improvement,
            metrics: newMetrics,
            simulatedTrades
        };
    }
    
    /**
     * محاكاة صفقة بمعاملات جديدة
     */
    simulateTradeWithNewParams(originalTrade, newParams) {
        // محاكاة بسيطة - في التطبيق الحقيقي ستكون أكثر تعقيداً
        let simulatedProfit = originalTrade.profit;
        
        // تأثير تغيير عتبة الربح
        if (originalTrade.profitBps < newParams.minProfitBps) {
            return null; // الصفقة لن تتم
        }
        
        // تأثير تغيير الانزلاق
        const slippageReduction = Math.max(0, originalTrade.slippage - newParams.maxSlippageBps / 10000);
        simulatedProfit += slippageReduction * originalTrade.amount;
        
        return {
            ...originalTrade,
            profit: simulatedProfit,
            simulatedWithNewParams: true
        };
    }
    
    /**
     * حساب نسبة التحسن
     */
    calculateImprovement(oldMetrics, newMetrics) {
        // وزن مختلف للمقاييس المختلفة
        const weights = {
            successRate: 0.3,
            averageProfit: 0.3,
            sharpeRatio: 0.2,
            maxDrawdown: -0.2 // سالب لأن الانخفاض أفضل
        };
        
        let totalImprovement = 0;
        
        for (const [metric, weight] of Object.entries(weights)) {
            if (oldMetrics[metric] !== 0) {
                const improvement = (newMetrics[metric] - oldMetrics[metric]) / Math.abs(oldMetrics[metric]);
                totalImprovement += improvement * weight;
            }
        }
        
        return totalImprovement;
    }
    
    /**
     * تحديث حالة السوق
     */
    async updateMarketState() {
        // في التطبيق الحقيقي، سيتم جلب البيانات من مصادر خارجية
        // هنا محاكاة بسيطة
        
        this.marketState = {
            volatility: this.calculateVolatility(),
            trend: this.calculateTrend(),
            liquidity: this.calculateLiquidity(),
            gasPrice: this.calculateGasPrice(),
            lastUpdate: Date.now()
        };
    }
    
    // وظائف مساعدة لحساب حالة السوق (محاكاة)
    calculateVolatility() {
        const rand = Math.random();
        if (rand < 0.2) return 'low';
        if (rand < 0.7) return 'normal';
        return 'high';
    }
    
    calculateTrend() {
        const rand = Math.random();
        if (rand < 0.3) return 'bearish';
        if (rand < 0.7) return 'neutral';
        return 'bullish';
    }
    
    calculateLiquidity() {
        const rand = Math.random();
        if (rand < 0.2) return 'low';
        if (rand < 0.8) return 'normal';
        return 'high';
    }
    
    calculateGasPrice() {
        const rand = Math.random();
        if (rand < 0.3) return 'low';
        if (rand < 0.7) return 'normal';
        return 'high';
    }
    
    /**
     * تسجيل نتيجة تداول
     */
    recordTrade(strategy, tradeData) {
        const performanceData = this.performanceData.get(strategy);
        if (performanceData) {
            performanceData.trades.push({
                ...tradeData,
                timestamp: Date.now()
            });
            
            // الاحتفاظ بآخر 1000 صفقة فقط
            if (performanceData.trades.length > 1000) {
                performanceData.trades = performanceData.trades.slice(-1000);
            }
            
            // تحديث المقاييس
            performanceData.metrics = this.calculateMetrics(performanceData.trades);
        }
    }
    
    /**
     * الحصول على المعاملات المحسنة لاستراتيجية
     */
    getOptimizedParams(strategy) {
        return this.optimizationParams.get(strategy);
    }
    
    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        const stats = {};
        
        for (const [strategy, data] of this.performanceData.entries()) {
            stats[strategy] = {
                metrics: data.metrics,
                lastOptimization: data.lastOptimization,
                optimizationResult: this.optimizationResults.get(strategy)
            };
        }
        
        return {
            strategies: stats,
            marketState: this.marketState,
            lastUpdate: Date.now()
        };
    }
    
    /**
     * تحليل الأداء الإجمالي
     */
    async analyzeOverallPerformance() {
        const allStrategies = Object.values(this.strategies);
        const overallMetrics = {
            totalTrades: 0,
            totalProfit: 0,
            averageSuccessRate: 0,
            bestStrategy: null,
            worstStrategy: null,
            diversificationScore: 0
        };

        let bestPerformance = -Infinity;
        let worstPerformance = Infinity;

        for (const strategy of allStrategies) {
            const data = this.performanceData.get(strategy);
            const metrics = data.metrics;

            overallMetrics.totalTrades += metrics.totalTrades;
            overallMetrics.totalProfit += metrics.totalProfit;

            // تحديد أفضل وأسوأ استراتيجية
            const performance = metrics.sharpeRatio;
            if (performance > bestPerformance) {
                bestPerformance = performance;
                overallMetrics.bestStrategy = strategy;
            }
            if (performance < worstPerformance) {
                worstPerformance = performance;
                overallMetrics.worstStrategy = strategy;
            }
        }

        // حساب متوسط معدل النجاح
        const successRates = allStrategies.map(s =>
            this.performanceData.get(s).metrics.successRate
        );
        overallMetrics.averageSuccessRate = successRates.reduce((sum, rate) => sum + rate, 0) / successRates.length;

        // حساب نقاط التنويع
        overallMetrics.diversificationScore = this.calculateDiversificationScore();

        this.overallPerformance = overallMetrics;
        return overallMetrics;
    }

    /**
     * حساب نقاط التنويع
     */
    calculateDiversificationScore() {
        const profits = Object.values(this.strategies).map(strategy => {
            return this.performanceData.get(strategy).metrics.totalProfit;
        });

        // حساب معامل الارتباط بين الاستراتيجيات
        const correlations = [];
        for (let i = 0; i < profits.length; i++) {
            for (let j = i + 1; j < profits.length; j++) {
                const correlation = this.calculateCorrelation(
                    this.performanceData.get(Object.values(this.strategies)[i]).trades,
                    this.performanceData.get(Object.values(this.strategies)[j]).trades
                );
                correlations.push(Math.abs(correlation));
            }
        }

        // نقاط التنويع = 1 - متوسط الارتباط
        const avgCorrelation = correlations.reduce((sum, corr) => sum + corr, 0) / correlations.length;
        return Math.max(0, 1 - avgCorrelation);
    }

    /**
     * حساب معامل الارتباط
     */
    calculateCorrelation(trades1, trades2) {
        const minLength = Math.min(trades1.length, trades2.length);
        if (minLength < 10) return 0;

        const profits1 = trades1.slice(-minLength).map(t => t.profit);
        const profits2 = trades2.slice(-minLength).map(t => t.profit);

        const mean1 = profits1.reduce((sum, p) => sum + p, 0) / profits1.length;
        const mean2 = profits2.reduce((sum, p) => sum + p, 0) / profits2.length;

        let numerator = 0;
        let sum1Sq = 0;
        let sum2Sq = 0;

        for (let i = 0; i < minLength; i++) {
            const diff1 = profits1[i] - mean1;
            const diff2 = profits2[i] - mean2;

            numerator += diff1 * diff2;
            sum1Sq += diff1 * diff1;
            sum2Sq += diff2 * diff2;
        }

        const denominator = Math.sqrt(sum1Sq * sum2Sq);
        return denominator === 0 ? 0 : numerator / denominator;
    }

    /**
     * إنتاج التوصيات
     */
    async generateRecommendations() {
        const recommendations = [];

        // تحليل الأداء الإجمالي
        if (this.overallPerformance.averageSuccessRate < 0.6) {
            recommendations.push({
                type: 'CRITICAL',
                category: 'OVERALL_PERFORMANCE',
                message: 'معدل النجاح الإجمالي منخفض - يحتاج مراجعة شاملة',
                actions: ['مراجعة جميع الاستراتيجيات', 'تشديد معايير الدخول', 'تحسين إدارة المخاطر']
            });
        }

        // تحليل التنويع
        if (this.overallPerformance.diversificationScore < 0.3) {
            recommendations.push({
                type: 'WARNING',
                category: 'DIVERSIFICATION',
                message: 'الاستراتيجيات مترابطة بشدة - مخاطر تركز عالية',
                actions: ['تطوير استراتيجيات غير مترابطة', 'تنويع الأصول', 'تنويع الأسواق']
            });
        }

        // تحليل كل استراتيجية
        for (const strategy of Object.values(this.strategies)) {
            const metrics = this.performanceData.get(strategy).metrics;

            if (metrics.successRate < 0.5) {
                recommendations.push({
                    type: 'HIGH',
                    category: 'STRATEGY_PERFORMANCE',
                    strategy,
                    message: `استراتيجية ${strategy} تحتاج تحسين عاجل`,
                    actions: ['مراجعة المعاملات', 'تحليل الأخطاء', 'اختبار معايير جديدة']
                });
            }

            if (metrics.maxDrawdown > 0.15) {
                recommendations.push({
                    type: 'HIGH',
                    category: 'RISK_MANAGEMENT',
                    strategy,
                    message: `مخاطر عالية في استراتيجية ${strategy}`,
                    actions: ['تقليل حجم المراكز', 'إضافة stop-loss', 'تحسين إدارة المخاطر']
                });
            }
        }

        // تحليل حالة السوق
        if (this.marketState.volatility === 'high') {
            recommendations.push({
                type: 'INFO',
                category: 'MARKET_CONDITIONS',
                message: 'تقلبات عالية في السوق - تعديل المعاملات مطلوب',
                actions: ['زيادة عتبات الربح', 'تقليل الانزلاق المسموح', 'زيادة الحذر']
            });
        }

        this.recommendations = recommendations;

        // إرسال التوصيات الحرجة
        const criticalRecommendations = recommendations.filter(r => r.type === 'CRITICAL');
        if (criticalRecommendations.length > 0) {
            this.emit('criticalRecommendations', criticalRecommendations);
        }

        return recommendations;
    }

    /**
     * تطبيق التوصيات تلقائياً
     */
    async applyRecommendationsAutomatically() {
        if (!this.config.autoApplyRecommendations) return;

        for (const recommendation of this.recommendations) {
            if (recommendation.autoApplicable) {
                await this.applyRecommendation(recommendation);
            }
        }
    }

    /**
     * تطبيق توصية محددة
     */
    async applyRecommendation(recommendation) {
        switch (recommendation.category) {
            case 'STRATEGY_PERFORMANCE':
                if (recommendation.strategy) {
                    const currentParams = this.optimizationParams.get(recommendation.strategy);
                    const adjustedParams = {
                        ...currentParams,
                        minProfitBps: currentParams.minProfitBps * 1.2,
                        riskLevel: Math.max(1, currentParams.riskLevel - 1)
                    };
                    this.optimizationParams.set(recommendation.strategy, adjustedParams);
                }
                break;

            case 'RISK_MANAGEMENT':
                if (recommendation.strategy) {
                    const currentParams = this.optimizationParams.get(recommendation.strategy);
                    const adjustedParams = {
                        ...currentParams,
                        riskLevel: Math.max(1, currentParams.riskLevel - 2),
                        maxSlippageBps: currentParams.maxSlippageBps * 0.8
                    };
                    this.optimizationParams.set(recommendation.strategy, adjustedParams);
                }
                break;
        }
    }

    /**
     * تصدير تقرير شامل
     */
    generateComprehensiveReport() {
        const report = {
            timestamp: Date.now(),
            marketState: this.marketState,
            overallPerformance: this.overallPerformance,
            strategies: {},
            recommendations: this.recommendations,
            optimizationHistory: {}
        };

        // إضافة بيانات كل استراتيجية
        for (const strategy of Object.values(this.strategies)) {
            const performanceData = this.performanceData.get(strategy);
            const optimizationResult = this.optimizationResults.get(strategy);

            report.strategies[strategy] = {
                metrics: performanceData.metrics,
                currentParams: this.optimizationParams.get(strategy),
                lastOptimization: optimizationResult,
                recentTrades: performanceData.trades.slice(-10)
            };
        }

        return report;
    }

    /**
     * إعادة تعيين إحصائيات استراتيجية
     */
    resetStrategyStats(strategy) {
        if (this.performanceData.has(strategy)) {
            const data = this.performanceData.get(strategy);
            data.trades = [];
            data.metrics = {
                totalTrades: 0,
                successfulTrades: 0,
                totalProfit: 0,
                totalLoss: 0,
                averageProfit: 0,
                successRate: 0,
                sharpeRatio: 0,
                maxDrawdown: 0,
                averageExecutionTime: 0,
                gasEfficiency: 0
            };

            console.log(`🔄 تم إعادة تعيين إحصائيات ${strategy}`);
        }
    }

    /**
     * تحديث معاملات استراتيجية يدوياً
     */
    updateStrategyParams(strategy, newParams) {
        if (this.optimizationParams.has(strategy)) {
            const currentParams = this.optimizationParams.get(strategy);
            const updatedParams = { ...currentParams, ...newParams };
            this.optimizationParams.set(strategy, updatedParams);

            console.log(`⚙️ تم تحديث معاملات ${strategy}`);
            return updatedParams;
        }
        return null;
    }

    /**
     * الحصول على أفضل استراتيجية حالياً
     */
    getBestPerformingStrategy() {
        let bestStrategy = null;
        let bestScore = -Infinity;

        for (const strategy of Object.values(this.strategies)) {
            const metrics = this.performanceData.get(strategy).metrics;

            // حساب نقاط مركبة للأداء
            const score = (metrics.successRate * 0.4) +
                         (metrics.sharpeRatio * 0.3) +
                         ((1 - metrics.maxDrawdown) * 0.3);

            if (score > bestScore) {
                bestScore = score;
                bestStrategy = strategy;
            }
        }

        return {
            strategy: bestStrategy,
            score: bestScore,
            metrics: bestStrategy ? this.performanceData.get(bestStrategy).metrics : null
        };
    }

    /**
     * إيقاف المحسن
     */
    stop() {
        if (this.optimizationTimer) clearInterval(this.optimizationTimer);
        if (this.marketTimer) clearInterval(this.marketTimer);

        console.log('⏹️ تم إيقاف محسن الاستراتيجيات');
    }
}

module.exports = AdvancedStrategyOptimizer;
