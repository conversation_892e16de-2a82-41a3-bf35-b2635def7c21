/**
 * @title API Client Configuration - تكوين عميل API المحسن
 * @description إعدادات آمنة ومحسنة لعميل API مع أفضل الممارسات
 * <AUTHOR> Team
 * @version 2.0.0
 */

require('dotenv').config();
const crypto = require('crypto');

/**
 * فئة تكوين عميل API المحسن
 */
class APIClientConfig {
    constructor() {
        this.environment = process.env.NODE_ENV || 'development';
        this.isProduction = this.environment === 'production';
        this.isDevelopment = this.environment === 'development';
        
        // التحقق من المتغيرات المطلوبة عند التهيئة
        this.validateRequiredVariables();
    }
    
    /**
     * الحصول على تكوين عميل API الكامل
     */
    getAPIClientConfig() {
        return {
            // إعدادات الاتصال الأساسية
            connection: this.getConnectionConfig(),
            
            // إعدادات الأمان والمصادقة
            security: this.getSecurityConfig(),
            
            // إعدادات إدارة الطلبات
            requestManagement: this.getRequestManagementConfig(),
            
            // إعدادات معالجة الأخطاء
            errorHandling: this.getErrorHandlingConfig(),
            
            // إعدادات التخزين المؤقت
            caching: this.getCachingConfig(),
            
            // إعدادات المراقبة والتسجيل
            monitoring: this.getMonitoringConfig(),
            
            // إعدادات البيئة
            environment: this.getEnvironmentConfig()
        };
    }
    
    /**
     * إعدادات الاتصال
     */
    getConnectionConfig() {
        // تحديد URL الأساسي حسب البيئة
        const baseURL = this.getBaseURL();
        
        return {
            // URL الأساسي للAPI
            baseURL,
            
            // مهلة الاتصال (timeout)
            timeout: parseInt(process.env.API_TIMEOUT) || (this.isProduction ? 30000 : 10000),
            
            // مهلة الاتصال الأولي
            connectTimeout: parseInt(process.env.API_CONNECT_TIMEOUT) || 5000,
            
            // عدد محاولات إعادة الاتصال
            maxRetries: parseInt(process.env.API_MAX_RETRIES) || (this.isProduction ? 3 : 1),
            
            // فترة الانتظار بين المحاولات (بالميلي ثانية)
            retryDelay: parseInt(process.env.API_RETRY_DELAY) || 1000,
            
            // إعدادات Keep-Alive
            keepAlive: process.env.API_KEEP_ALIVE !== 'false',
            keepAliveMsecs: parseInt(process.env.API_KEEP_ALIVE_MS) || 1000,
            
            // إعدادات HTTP Agent
            maxSockets: parseInt(process.env.API_MAX_SOCKETS) || 10,
            maxFreeSockets: parseInt(process.env.API_MAX_FREE_SOCKETS) || 5
        };
    }
    
    /**
     * إعدادات الأمان والمصادقة
     */
    getSecurityConfig() {
        return {
            // مفتاح API (مع التحقق من وجوده)
            apiKey: this.getAPIKey(),
            
            // JWT Token (اختياري)
            jwtToken: process.env.API_JWT_TOKEN,
            
            // إعدادات Headers الأمنية
            securityHeaders: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': `FlashBot-Advanced-2025/${this.getVersion()}`,
                'X-Client-Version': this.getVersion(),
                'X-Environment': this.environment
            },
            
            // إعدادات HTTPS
            httpsAgent: {
                rejectUnauthorized: this.isProduction,
                keepAlive: true,
                maxSockets: 10
            },
            
            // إعدادات التشفير
            encryption: {
                algorithm: 'aes-256-gcm',
                key: this.getEncryptionKey(),
                enabled: process.env.API_ENCRYPTION_ENABLED === 'true'
            },
            
            // إعدادات Rate Limiting من جانب العميل
            rateLimiting: {
                enabled: process.env.CLIENT_RATE_LIMIT_ENABLED !== 'false',
                maxRequestsPerSecond: parseInt(process.env.CLIENT_MAX_RPS) || 10,
                maxRequestsPerMinute: parseInt(process.env.CLIENT_MAX_RPM) || 100,
                burstLimit: parseInt(process.env.CLIENT_BURST_LIMIT) || 20
            }
        };
    }
    
    /**
     * إعدادات إدارة الطلبات
     */
    getRequestManagementConfig() {
        return {
            // إعدادات إعادة المحاولة
            retry: {
                enabled: process.env.API_RETRY_ENABLED !== 'false',
                maxAttempts: parseInt(process.env.API_MAX_RETRY_ATTEMPTS) || 3,
                baseDelay: parseInt(process.env.API_RETRY_BASE_DELAY) || 1000,
                maxDelay: parseInt(process.env.API_RETRY_MAX_DELAY) || 10000,
                exponentialBackoff: process.env.API_EXPONENTIAL_BACKOFF !== 'false',
                retryCondition: this.getRetryCondition()
            },
            
            // إعدادات Circuit Breaker
            circuitBreaker: {
                enabled: process.env.API_CIRCUIT_BREAKER_ENABLED === 'true',
                failureThreshold: parseInt(process.env.API_FAILURE_THRESHOLD) || 5,
                resetTimeout: parseInt(process.env.API_RESET_TIMEOUT) || 60000,
                monitoringPeriod: parseInt(process.env.API_MONITORING_PERIOD) || 10000
            },
            
            // إعدادات Queue للطلبات
            requestQueue: {
                enabled: process.env.API_QUEUE_ENABLED === 'true',
                maxSize: parseInt(process.env.API_QUEUE_MAX_SIZE) || 100,
                concurrency: parseInt(process.env.API_QUEUE_CONCURRENCY) || 5,
                priority: process.env.API_QUEUE_PRIORITY === 'true'
            }
        };
    }
    
    /**
     * إعدادات معالجة الأخطاء
     */
    getErrorHandlingConfig() {
        return {
            // تسجيل الأخطاء
            logging: {
                enabled: process.env.API_ERROR_LOGGING !== 'false',
                level: process.env.API_ERROR_LOG_LEVEL || 'error',
                includeStack: this.isDevelopment,
                includeRequest: this.isDevelopment,
                includeResponse: this.isDevelopment && process.env.API_LOG_RESPONSE === 'true'
            },
            
            // إعدادات التنبيهات
            alerts: {
                enabled: process.env.API_ERROR_ALERTS === 'true',
                webhook: process.env.API_ERROR_WEBHOOK_URL,
                email: process.env.API_ERROR_EMAIL,
                threshold: parseInt(process.env.API_ERROR_THRESHOLD) || 5
            },
            
            // إعدادات Fallback
            fallback: {
                enabled: process.env.API_FALLBACK_ENABLED === 'true',
                endpoints: this.getFallbackEndpoints(),
                strategy: process.env.API_FALLBACK_STRATEGY || 'round_robin'
            }
        };
    }
    
    /**
     * إعدادات التخزين المؤقت
     */
    getCachingConfig() {
        return {
            // تفعيل التخزين المؤقت
            enabled: process.env.API_CACHE_ENABLED !== 'false',
            
            // نوع التخزين المؤقت
            type: process.env.API_CACHE_TYPE || 'memory', // memory, redis, file
            
            // إعدادات TTL (Time To Live)
            ttl: {
                default: parseInt(process.env.API_CACHE_TTL) || 300000, // 5 دقائق
                short: parseInt(process.env.API_CACHE_TTL_SHORT) || 60000, // دقيقة
                long: parseInt(process.env.API_CACHE_TTL_LONG) || 3600000 // ساعة
            },
            
            // إعدادات Memory Cache
            memory: {
                maxSize: parseInt(process.env.API_CACHE_MAX_SIZE) || 100,
                checkPeriod: parseInt(process.env.API_CACHE_CHECK_PERIOD) || 600000
            },
            
            // إعدادات Redis Cache
            redis: {
                url: process.env.API_CACHE_REDIS_URL || process.env.REDIS_URL,
                keyPrefix: process.env.API_CACHE_KEY_PREFIX || 'flashbot:api:',
                enabled: process.env.API_CACHE_REDIS_ENABLED === 'true'
            }
        };
    }
    
    /**
     * إعدادات المراقبة والتسجيل
     */
    getMonitoringConfig() {
        return {
            // تسجيل الطلبات
            requestLogging: {
                enabled: process.env.API_REQUEST_LOGGING !== 'false',
                level: process.env.API_REQUEST_LOG_LEVEL || (this.isDevelopment ? 'debug' : 'info'),
                includeHeaders: this.isDevelopment,
                includeBody: this.isDevelopment && process.env.API_LOG_BODY === 'true',
                sanitizeHeaders: ['authorization', 'x-api-key', 'cookie']
            },
            
            // مقاييس الأداء
            metrics: {
                enabled: process.env.API_METRICS_ENABLED !== 'false',
                collectInterval: parseInt(process.env.API_METRICS_INTERVAL) || 60000,
                historySize: parseInt(process.env.API_METRICS_HISTORY) || 100
            },
            
            // Health Check
            healthCheck: {
                enabled: process.env.API_HEALTH_CHECK_ENABLED !== 'false',
                interval: parseInt(process.env.API_HEALTH_CHECK_INTERVAL) || 30000,
                timeout: parseInt(process.env.API_HEALTH_CHECK_TIMEOUT) || 5000,
                endpoint: process.env.API_HEALTH_CHECK_ENDPOINT || '/health'
            }
        };
    }
    
    /**
     * إعدادات البيئة
     */
    getEnvironmentConfig() {
        return {
            nodeEnv: this.environment,
            isProduction: this.isProduction,
            isDevelopment: this.isDevelopment,
            isTest: this.environment === 'test',
            version: this.getVersion(),
            buildTime: process.env.BUILD_TIME || new Date().toISOString(),
            gitCommit: process.env.GIT_COMMIT || 'unknown'
        };
    }
    
    /**
     * الحصول على URL الأساسي حسب البيئة
     */
    getBaseURL() {
        // أولوية للمتغير المخصص
        if (process.env.API_BASE_URL) {
            return process.env.API_BASE_URL;
        }
        
        // تحديد URL حسب البيئة
        switch (this.environment) {
            case 'production':
                return process.env.API_PRODUCTION_URL || 'https://api.flashbot.com';
            case 'staging':
                return process.env.API_STAGING_URL || 'https://staging-api.flashbot.com';
            case 'test':
                return process.env.API_TEST_URL || 'http://localhost:3001';
            default: // development
                return process.env.API_DEV_URL || 'http://localhost:3000';
        }
    }
    
    /**
     * الحصول على مفتاح API مع التحقق
     */
    getAPIKey() {
        const apiKey = process.env.FLASHBOT_API_KEY || process.env.API_KEY;
        
        if (!apiKey) {
            if (this.isProduction) {
                throw new Error('مفتاح API مطلوب في بيئة الإنتاج (FLASHBOT_API_KEY)');
            } else {
                console.warn('⚠️ تحذير: لم يتم تعيين مفتاح API - سيتم استخدام مفتاح تجريبي');
                return 'dev-api-key-' + Date.now();
            }
        }
        
        // التحقق من صحة تنسيق مفتاح API
        if (apiKey.length < 32) {
            throw new Error('مفتاح API قصير جداً - يجب أن يكون 32 حرف على الأقل');
        }
        
        return apiKey;
    }
    
    /**
     * الحصول على مفتاح التشفير
     */
    getEncryptionKey() {
        const key = process.env.API_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY;
        
        if (!key && process.env.API_ENCRYPTION_ENABLED === 'true') {
            throw new Error('مفتاح التشفير مطلوب عند تفعيل التشفير');
        }
        
        return key ? crypto.createHash('sha256').update(key).digest() : null;
    }
    
    /**
     * الحصول على إصدار التطبيق
     */
    getVersion() {
        return process.env.APP_VERSION || '2.0.0';
    }
    
    /**
     * شروط إعادة المحاولة
     */
    getRetryCondition() {
        return (error) => {
            // إعادة المحاولة للأخطاء المؤقتة
            if (!error.response) return true; // خطأ شبكة
            
            const status = error.response.status;
            
            // إعادة المحاولة لرموز الحالة المؤقتة
            return [408, 429, 500, 502, 503, 504].includes(status);
        };
    }
    
    /**
     * الحصول على نقاط النهاية البديلة
     */
    getFallbackEndpoints() {
        const fallbackUrls = process.env.API_FALLBACK_URLS;
        
        if (!fallbackUrls) return [];
        
        return fallbackUrls.split(',').map(url => url.trim());
    }
    
    /**
     * التحقق من المتغيرات المطلوبة
     */
    validateRequiredVariables() {
        const requiredVars = [];
        
        // متغيرات مطلوبة في الإنتاج فقط
        if (this.isProduction) {
            if (!process.env.FLASHBOT_API_KEY && !process.env.API_KEY) {
                requiredVars.push('FLASHBOT_API_KEY أو API_KEY');
            }
            
            if (!process.env.API_PRODUCTION_URL && !process.env.API_BASE_URL) {
                requiredVars.push('API_PRODUCTION_URL أو API_BASE_URL');
            }
        }
        
        // متغيرات مطلوبة عند تفعيل التشفير
        if (process.env.API_ENCRYPTION_ENABLED === 'true') {
            if (!process.env.API_ENCRYPTION_KEY && !process.env.ENCRYPTION_KEY) {
                requiredVars.push('API_ENCRYPTION_KEY أو ENCRYPTION_KEY');
            }
        }
        
        if (requiredVars.length > 0) {
            throw new Error(`متغيرات البيئة المطلوبة مفقودة: ${requiredVars.join(', ')}`);
        }
    }
    
    /**
     * طباعة ملخص التكوين
     */
    printConfigSummary() {
        const config = this.getAPIClientConfig();
        
        console.log('🔧 ملخص تكوين عميل API:');
        console.log(`   🌐 البيئة: ${config.environment.nodeEnv}`);
        console.log(`   🔗 URL الأساسي: ${config.connection.baseURL}`);
        console.log(`   🔑 مفتاح API: ${config.security.apiKey ? '✅ موجود' : '❌ مفقود'}`);
        console.log(`   ⏱️ مهلة الاتصال: ${config.connection.timeout}ms`);
        console.log(`   🔄 إعادة المحاولة: ${config.requestManagement.retry.enabled ? 'مفعل' : 'معطل'}`);
        console.log(`   💾 التخزين المؤقت: ${config.caching.enabled ? 'مفعل' : 'معطل'}`);
        console.log(`   📊 المراقبة: ${config.monitoring.metrics.enabled ? 'مفعل' : 'معطل'}`);
        console.log(`   🔒 التشفير: ${config.security.encryption.enabled ? 'مفعل' : 'معطل'}`);
    }
}

// تصدير instance واحد
const apiClientConfig = new APIClientConfig();

module.exports = {
    APIClientConfig,
    getAPIClientConfig: () => apiClientConfig.getAPIClientConfig(),
    printConfigSummary: () => apiClientConfig.printConfigSummary(),
    validateConfig: () => apiClientConfig.validateRequiredVariables()
};
